/* Table Of Content - start
================================================== */
/* 
* Project Name   :  Coinpay - Crypto Currency Site Template.
* File           :  CSS Base
* Version        :  1.0.0
* Author         :  XpressBuddy (https://themeforest.net/user/xpressbuddy/portfolio)
* Developer			 :	webrok (https://www.fiverr.com/webrok?up_rollout=true)

==================================================

1 - Template Global Settings
* - 1.01 - Template Fonts
* - 1.02 - Template Reset

2 - Template Blocks
* - 2.01 - CSS Animations
* - 2.02 - Spacing and Gaps
* - 2.03 - Ordered and Unordered list
* - 2.04 - Button
* - 2.05 - Video
* - 2.06 - Typography
* - 2.07 - Form
* - 2.08 - Social
* - 2.09 - Pagelist
* - 2.10 - Iconlist
* - 2.11 - Tab
* - 2.12 - Iconbox
* - 2.13 - Event
* - 2.14 - Roadmap
* - 2.15 - Countdown
* - 2.16 - Accordion
* - 2.17 - ProgressBar
* - 2.18 - Authorbox
* - 2.19 - Pagination
* - 2.20 - Team
* - 2.21 - Partnerlogo
* - 2.22 - Tokenomics
* - 2.23 - Blog

3 - Template Parts
* - 3.01 - Site Header
* - 3.02 - Site Footer
* - 3.03 - Page Header
* - 3.04 - Breadcrumb
* - 3.05 - Sidebar

4 - Template Patterns
* - 4.01 - Hero Section
* - 4.02 - Countdown Section
* - 4.03 - About Section
* - 4.04 - Table Section
* - 4.05 - Chart Section
* - 4.06 - Service Section
* - 4.07 - Content Ticker Section
* - 4.08 - Tokenomics Section
* - 4.09 - Feature Section
* - 4.10 - Roadmap Section
* - 4.11 - Testimonial Section

5 - Templates
* - 5.01 - Home Pages
* - 5.02 - Details Pages
* - 5.03 - Contact Page
* - 5.04 - Register Pages

*/
/* Table Of Content - end
================================================== */
@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Dela+Gothic+One&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:opsz,wght@12..96,200..800&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:opsz,wght@12..96,200..800&amp;family=Lilita+One&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&amp;display=swap");
:root {
  --bs-body-font-family: "DM Sans", serif;
  --bs-heading-font-family: "DM Sans", serif;
  --bs-body-font-size: 16px;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 26px;
  --bs-transition: 400ms ease;
}

/* 1.01 - Template Fonts - Start
================================================== */
@font-face {
  font-family: "Roobert PRO Bold";
  src: url("../fonts/RoobertPRO-Bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Roobert PRO Medium";
  src: url("../fonts/RoobertPRO-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "January Night";
  src: url("../fonts/January-Night.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
/* 1.01 - Template Fonts - End
================================================== */
/* 1.02 - Template Reset - Start
================================================== */
body {
  margin: 0;
  padding: 0;
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  font-style: normal;
  font-optical-sizing: auto;
  color: var(--bs-body-color);
  font-family: var(--bs-body-font-family);
  background-color: var(--bs-body-bg);
  text-rendering: optimizelegibility;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

.page_wrapper {
  overflow: hidden;
  position: relative;
}

iframe {
  width: 100%;
  border: none;
  display: block;
  min-height: 440px;
}

.iframe_block {
  overflow: hidden;
  border-radius: 20px;
  margin-bottom: 70px;
}

a:focus,
a:active,
input,
input:hover,
input:focus,
input:active,
textarea,
textarea:hover,
textarea:focus,
textarea:active {
  outline: none;
}

img:not([draggable]),
embed,
object,
video {
  height: auto;
  max-width: 100%;
}

img {
  border: none;
  height: auto;
  max-width: 100%;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  vertical-align: middle;
}

a {
  outline: 0;
  display: inline-block;
  text-decoration: none;
  color: var(--bs-heading-color);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  font-family: var(--bs-body-font-family);
}
a:active, a:focus, a:hover, a:visited {
  outline: 0;
  text-decoration: none;
}

button {
  padding: 0;
  border: none;
  outline: none;
  background: none;
  display: inline-block;
  color: var(--bs-heading-color);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  font-family: var(--bs-body-font-family);
}
button:focus {
  outline: none;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}

b, strong {
  font-weight: 700;
}

p strong {
  font-size: 18px;
  font-weight: 500;
}

hr {
  opacity: 1;
  height: 1px;
  border: none;
  margin: 38px 0;
  background-color: var(--bs-border-color);
}

mark {
  padding: 0;
  font-weight: 700;
  color: var(--bs-primary);
  background-color: transparent;
}

.container {
  max-width: 1320px;
  padding-left: 15px;
  padding-right: 15px;
}

.container-fluid {
  padding-left: 30px;
  padding-right: 30px;
}

.row {
  margin: -15px;
}

[class*=col-] {
  padding: 15px;
}

.backtotop {
  right: 15px;
  z-index: 999;
  bottom: 60px;
  display: none;
  position: fixed;
}

.backtotop .scroll {
  z-index: 1;
  width: 42px;
  height: 42px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 16px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.backtotop .scroll:hover {
  color: var(--bs-dark);
  border-color: var(--bs-white);
  background-color: var(--bs-white);
}

#preloader {
  inset: 0;
  gap: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  z-index: 9999;
  position: fixed;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-dark);
}

.line-1,
.line-2,
.line-3,
.line-4 {
  width: 5px;
  height: 30px;
  background: var(--bs-secondary);
  -webkit-animation: scaleUpDown 1.2s infinite ease-in-out;
          animation: scaleUpDown 1.2s infinite ease-in-out;
}

.line-2 {
  -webkit-animation-delay: 0.2s;
          animation-delay: 0.2s;
}

.line-3 {
  -webkit-animation-delay: 0.4s;
          animation-delay: 0.4s;
}

.line-4 {
  -webkit-animation-delay: 0.6s;
          animation-delay: 0.6s;
}

@-webkit-keyframes scaleUpDown {
  0%, 100% {
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  50% {
    -webkit-transform: scaleY(2);
            transform: scaleY(2);
  }
}

@keyframes scaleUpDown {
  0%, 100% {
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  50% {
    -webkit-transform: scaleY(2);
            transform: scaleY(2);
  }
}
.dropdown-menu {
  border: none;
  padding: 6px 0;
  margin-top: 16px;
  border-radius: 6px;
  background-color: var(--bs-white);
  -webkit-box-shadow: 0 30px 50px rgba(0, 0, 0, 0.6);
          box-shadow: 0 30px 50px rgba(0, 0, 0, 0.6);
  -webkit-animation: 0.2s ease-in-out 0s normal none 1 running fadeIn;
          animation: 0.2s ease-in-out 0s normal none 1 running fadeIn;
}
.dropdown-menu:before {
  left: 0;
  right: 0;
  top: -16px;
  content: "";
  height: 16px;
  display: block;
  position: absolute;
}
.dropdown-menu > li {
  padding: 0 6px;
}
.dropdown-menu > li:not(:last-child) {
  margin-bottom: 1px;
}
.dropdown-menu > li > .dropdown-item {
  display: block;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  position: relative;
  border-radius: 4px;
  white-space: nowrap;
  color: var(--bs-dark);
  padding: 12px 18px 11px;
}
.dropdown-menu > li > .dropdown-item .nav_link_icon {
  color: inherit;
}
.dropdown-menu > li.active > .dropdown-item, .dropdown-menu > li:hover > .dropdown-item {
  color: var(--bs-white);
  background-color: var(--bs-primary);
}
.dropdown-menu > li.active > .dropdown-item .nav_link_icon, .dropdown-menu > li:hover > .dropdown-item .nav_link_icon {
  color: var(--bs-white);
}

.section_decoration {
  z-index: 1;
  position: relative;
}
.section_decoration .decoration_item {
  z-index: -1;
  position: absolute;
}
.section_decoration .decoration_item img[src*=shape_section_divider_] {
  width: 100%;
}
.section_decoration .decoration_item:has(img[src*=shape_section_divider_]) {
  top: 0;
  left: 0;
  right: 0;
}
.section_decoration .shape_net_top {
  left: 0;
  right: 0;
  top: -194px;
}
.section_decoration .shape_net_top img {
  width: 100%;
  height: auto;
}

/* 1.02 - Template Reset - End
================================================== */
/* 2.01 - CSS Animations - Start
================================================== */
@-webkit-keyframes upDownMover {
  0%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
  }
}
@keyframes upDownMover {
  0%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
  }
}
.upDownMover {
  -webkit-animation: upDownMover 4s ease-in-out infinite;
          animation: upDownMover 4s ease-in-out infinite;
}

@-webkit-keyframes fadeInOut {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
.fadeInOut {
  -webkit-animation: fadeInOut 4s ease-in-out infinite;
          animation: fadeInOut 4s ease-in-out infinite;
}

@-webkit-keyframes gradientChange {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradientChange {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
.gradientChange {
  -webkit-animation: gradientChange 8s ease infinite;
          animation: gradientChange 8s ease infinite;
}

.section_shadow_top {
  z-index: 1;
  position: relative;
}
.section_shadow_top:before {
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
  content: "";
  height: 125px;
  opacity: 0.4;
  position: absolute;
  -webkit-filter: blur(44px);
          filter: blur(44px);
  border-radius: 50px;
  background-size: 400% 400%;
  -webkit-animation: gradientChange 8s ease infinite;
          animation: gradientChange 8s ease infinite;
  background-image: linear-gradient(45deg, var(--bs-primary), var(--bs-secondary));
}

@-webkit-keyframes animationSpin {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@keyframes animationSpin {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
.animationSpin {
  -webkit-animation: spin 5000ms linear infinite;
          animation: spin 5000ms linear infinite;
}

@-webkit-keyframes ripple {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(1.75);
            transform: scale(1.75);
  }
}

@keyframes ripple {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(1.75);
            transform: scale(1.75);
  }
}
@-webkit-keyframes coinFlip {
  0% {
    -webkit-transform: rotateY(-180deg);
            transform: rotateY(-180deg);
  }
  80% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg);
  }
  100% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg);
  }
}
@keyframes coinFlip {
  0% {
    -webkit-transform: rotateY(-180deg);
            transform: rotateY(-180deg);
  }
  80% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg);
  }
  100% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg);
  }
}
@-webkit-keyframes bellRing {
  0% {
    -webkit-transform: rotate(0) scale(1) skew(1deg);
            transform: rotate(0) scale(1) skew(1deg);
  }
  10% {
    -webkit-transform: rotate(-15deg) scale(1) skew(1deg);
            transform: rotate(-15deg) scale(1) skew(1deg);
  }
  20% {
    -webkit-transform: rotate(30deg) scale(1) skew(1deg);
            transform: rotate(30deg) scale(1) skew(1deg);
  }
  30% {
    -webkit-transform: rotate(-15deg) scale(1) skew(1deg);
            transform: rotate(-15deg) scale(1) skew(1deg);
  }
  40% {
    -webkit-transform: rotate(30deg) scale(1) skew(1deg);
            transform: rotate(30deg) scale(1) skew(1deg);
  }
  100%, 50% {
    -webkit-transform: rotate(0) scale(1) skew(1deg);
            transform: rotate(0) scale(1) skew(1deg);
  }
}
@keyframes bellRing {
  0% {
    -webkit-transform: rotate(0) scale(1) skew(1deg);
            transform: rotate(0) scale(1) skew(1deg);
  }
  10% {
    -webkit-transform: rotate(-15deg) scale(1) skew(1deg);
            transform: rotate(-15deg) scale(1) skew(1deg);
  }
  20% {
    -webkit-transform: rotate(30deg) scale(1) skew(1deg);
            transform: rotate(30deg) scale(1) skew(1deg);
  }
  30% {
    -webkit-transform: rotate(-15deg) scale(1) skew(1deg);
            transform: rotate(-15deg) scale(1) skew(1deg);
  }
  40% {
    -webkit-transform: rotate(30deg) scale(1) skew(1deg);
            transform: rotate(30deg) scale(1) skew(1deg);
  }
  100%, 50% {
    -webkit-transform: rotate(0) scale(1) skew(1deg);
            transform: rotate(0) scale(1) skew(1deg);
  }
}
.bellRing {
  -webkit-transform-origin: top;
          transform-origin: top;
  -webkit-animation: ring 1.8s ease-out infinite;
          animation: ring 1.8s ease-out infinite;
}

@-webkit-keyframes smallZoomInOut {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.04);
            transform: scale(1.04);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}

@keyframes smallZoomInOut {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.04);
            transform: scale(1.04);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
.smallZoomInOut {
  -webkit-animation: smallZoomInOut 1s infinite ease-in-out;
          animation: smallZoomInOut 1s infinite ease-in-out;
}

/* 2.01 - CSS Animations - End
================================================== */
/* 2.02 - Spacing and Gaps - Start
================================================== */
.section_space {
  padding-top: 150px;
  padding-bottom: 150px;
}

/* 2.02 - Spacing and Gaps - End
================================================== */
/* 2.03 - Ordered and Unordered list - Start
================================================== */
[class*=unordered_list] {
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
[class*=unordered_list] > li {
  float: left;
  list-style: none;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.unordered_list_block {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.unordered_list_block > li {
  width: 100%;
  display: block;
}

.contact_info_list {
  gap: 20px;
}
.contact_info_list a {
  gap: 10px;
  font-size: 20px;
  font-weight: 700;
  line-height: 24px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.contact_info_list a:hover {
  color: var(--bs-primary);
}
.contact_info_list a i {
  color: var(--bs-primary);
}

.category_list_block > li {
  border-bottom: 1px solid var(--bs-border-color);
}
.category_list_block > li > a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 12px 0;
  font-size: 16px;
  list-style: 24px;
  font-weight: 700;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.category_list_block > li > a > * {
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.category_list_block > li:hover > a > * {
  color: var(--bs-secondary);
}
.category_list_block > li:hover > a .label {
  -webkit-transform: translateX(18px);
          transform: translateX(18px);
}
.category_list_block > li:hover > a .icon {
  opacity: 1;
}
.category_list_block .icon {
  left: 0;
  top: 13px;
  opacity: 0;
  position: absolute;
}

.tags_block {
  gap: 10px;
}
.tags_block a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  padding: 6px 16px;
  border-radius: 6px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.tags_block a:hover {
  color: var(--bs-white);
  border-color: var(--bs-secondary);
  background-color: var(--bs-secondary);
}

.post_meta {
  gap: 14px 40px;
}
.post_meta > li {
  line-height: 1;
  font-size: 16px;
  font-weight: 500;
}
.post_meta a {
  gap: 8px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.post_meta a:hover {
  color: var(--bs-primary);
}
.post_meta a.post_category {
  font-weight: 600;
  color: var(--bs-primary);
  text-transform: uppercase;
}
.post_meta i {
  color: var(--bs-primary);
}

.post_meta.style_2 > li {
  position: relative;
}
.post_meta.style_2 > li:not(:last-child):after {
  top: 0;
  bottom: 0;
  width: 2px;
  content: "";
  right: -21px;
  position: absolute;
  background-color: #DADAE5;
}

/* 2.03 - Ordered and Unordered list - End
================================================== */
/* 2.04 - Button - Start
================================================== */
.btns_group {
  gap: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 60px 0;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.btn {
  gap: 6px;
  line-height: 1;
  font-size: 18px;
  font-weight: 600;
  padding: 15px 30px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 50px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-white);
  border-color: var(--bs-secondary);
  background-color: var(--bs-secondary);
}
.btn:hover {
  color: var(--bs-white);
  border-color: var(--bs-primary);
  background-color: var(--bs-primary);
}

.btn_link {
  gap: 10px;
  line-height: 1;
  font-size: 18px;
  font-weight: 600;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-bottom: 2px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-bottom: 1px solid var(--bs-body-color);
}
.btn_link > * {
  color: var(--bs-body-color);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.btn_link:hover {
  border-color: var(--bs-primary);
}
.btn_link:hover > * {
  color: var(--bs-primary);
}
.btn_link:hover .btn_icon {
  -webkit-transform: translateX(2px);
          transform: translateX(2px);
}

.btn_login_google {
  gap: 10px;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  padding: 11px 30px;
  border-radius: 6px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}
.btn_login_google .icon {
  width: 20px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}
.btn_login_google .icon img {
  width: 100%;
}

@-webkit-keyframes btnMaskOffHover {
  from {
    -webkit-mask-position: 100% 0;
            mask-position: 100% 0;
  }
  to {
    -webkit-mask-position: 0 0;
            mask-position: 0 0;
  }
}

@keyframes btnMaskOffHover {
  from {
    -webkit-mask-position: 100% 0;
            mask-position: 100% 0;
  }
  to {
    -webkit-mask-position: 0 0;
            mask-position: 0 0;
  }
}
@-webkit-keyframes btnMaskOnHover {
  from {
    -webkit-mask-position: 0 0;
            mask-position: 0 0;
  }
  to {
    -webkit-mask-position: 100% 0;
            mask-position: 100% 0;
  }
}
@keyframes btnMaskOnHover {
  from {
    -webkit-mask-position: 0 0;
            mask-position: 0 0;
  }
  to {
    -webkit-mask-position: 100% 0;
            mask-position: 100% 0;
  }
}
.memecoin_btn {
  z-index: 1;
  font-size: 20px;
  overflow: hidden;
  font-weight: 600;
  line-height: 24px;
  text-align: center;
  position: relative;
  padding: 23px 50px;
  border-radius: 50px;
  font-family: var(--bs-body-font-family);
}
.memecoin_btn .btn_bg {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  position: absolute;
  -webkit-animation: btnMaskOffHover 0.6s steps(70) forwards;
          animation: btnMaskOffHover 0.6s steps(70) forwards;
}
.memecoin_btn:hover .btn_bg {
  -webkit-animation: btnMaskOnHover 0.6s steps(70) forwards;
          animation: btnMaskOnHover 0.6s steps(70) forwards;
}

.memecoin_btn.bg-white {
  color: var(--bs-dark);
}
.memecoin_btn.bg-white .btn_bg {
  background: var(--bs-secondary);
  -webkit-mask-size: 7100%, 100%;
          mask-size: 7100%, 100%;
}

.memecoin_btn.bg-light:hover {
  color: var(--bs-dark);
}
.memecoin_btn.bg-light .btn_bg {
  background: var(--bs-dark);
  -webkit-mask-size: 7100%, 100%;
          mask-size: 7100%, 100%;
}

.pepecoin_btn {
  z-index: 1;
  line-height: 1;
  font-size: 24px;
  font-weight: 400;
  position: relative;
  padding: 19px 38px 18px;
  text-transform: uppercase;
  font-family: var(--bs-heading-font-family);
}
.pepecoin_btn .btn_bg {
  inset: 0;
  z-index: -1;
  position: absolute;
}
.pepecoin_btn .btn_bg svg {
  width: 100%;
  height: 100%;
  display: block;
  -webkit-transition: 0.2s ease-in-out;
  transition: 0.2s ease-in-out;
}
.pepecoin_btn:hover {
  -webkit-transform: translate(-1px, -1px);
          transform: translate(-1px, -1px);
}
.pepecoin_btn:hover .btn_bg svg {
  -webkit-filter: drop-shadow(6px 6px 0px var(--bs-dark));
          filter: drop-shadow(6px 6px 0px var(--bs-dark));
}

.ico_btn_outline {
  gap: 8px;
  z-index: 1;
  line-height: 1;
  font-size: 20px;
  font-weight: 700;
  border-radius: 6px;
  padding: 20px 40px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
  background: -webkit-gradient(linear, left top, right top, from(var(--bs-primary)), to(var(--bs-secondary)));
  background: linear-gradient(90deg, var(--bs-primary), var(--bs-secondary));
}
.ico_btn_outline:before {
  inset: 1px;
  z-index: -1;
  content: "";
  position: absolute;
  border-radius: 6px;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  background: -webkit-gradient(linear, left top, right top, from(rgba(var(--bs-secondary-rgb), 0.96)), color-stop(24%, var(--bs-dark)));
  background: linear-gradient(90deg, rgba(var(--bs-secondary-rgb), 0.96) 0%, var(--bs-dark) 24%);
}
.ico_btn_outline:hover {
  color: var(--bs-white);
}
.ico_btn_outline:hover:before {
  opacity: 0;
}

.ico_creative_btn {
  z-index: 1;
  content: "";
  padding: 1px;
  font-size: 16px;
  font-weight: 700;
  overflow: hidden;
  border-radius: 6px;
  text-align: center;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-primary);
  -webkit-box-shadow: 0px -8px 18px -16px rgba(var(--bs-primary-rgb), 1);
          box-shadow: 0px -8px 18px -16px rgba(var(--bs-primary-rgb), 1);
  background: -webkit-gradient(linear, left top, right top, from(var(--bs-primary)), to(var(--bs-secondary)));
  background: linear-gradient(90deg, var(--bs-primary), var(--bs-secondary));
}
.ico_creative_btn .btn_wrapper {
  z-index: 1;
  padding: 10px;
  overflow: hidden;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-radius: inherit;
  background-color: var(--bs-dark);
}
.ico_creative_btn .btn_wrapper:before {
  left: 0;
  right: 0;
  top: 60%;
  height: 100%;
  content: "";
  z-index: -1;
  opacity: 0.3;
  position: absolute;
  -webkit-filter: blur(12px);
          filter: blur(12px);
  border-radius: 50%;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  background-image: radial-gradient(#FF3BD4, #7130C3, transparent);
}
.ico_creative_btn [class*=btn_icon_] {
  width: 66px;
  height: 40px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.ico_creative_btn [class*=dot_] {
  width: 5px;
  height: 6px;
  position: absolute;
  border-radius: 5px;
  display: inline-block;
  border: 1px solid #2A246D;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.ico_creative_btn .btn_icon_left .dot_top {
  top: 0;
  left: 0;
}
.ico_creative_btn .btn_icon_left .dot_bottom {
  left: 0;
  bottom: 0;
}
.ico_creative_btn .btn_icon_right .dot_top {
  top: 0;
  right: 0;
}
.ico_creative_btn .btn_icon_right .dot_bottom {
  right: 0;
  bottom: 0;
}
.ico_creative_btn [class*=icon_arrow_] {
  width: 25px;
  overflow: visible;
  fill: transparent;
}
.ico_creative_btn [class*=icon_arrow_] path {
  stroke: var(--bs-primary);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.ico_creative_btn .icon_arrow_left {
  -webkit-transform: translateX(4px);
          transform: translateX(4px);
}
.ico_creative_btn .icon_arrow_right {
  -webkit-transform: translateX(-4px);
          transform: translateX(-4px);
}
.ico_creative_btn:hover {
  color: var(--bs-white);
  -webkit-transform: translateY(-6px);
          transform: translateY(-6px);
  -webkit-box-shadow: 0px 24px 26px -20px rgba(var(--bs-primary-rgb), 0.6);
          box-shadow: 0px 24px 26px -20px rgba(var(--bs-primary-rgb), 0.6);
  background: -webkit-gradient(linear, left bottom, left top, from(var(--bs-primary)), to(var(--bs-secondary)));
  background: linear-gradient(0deg, var(--bs-primary), var(--bs-secondary));
}
.ico_creative_btn:hover .btn_wrapper:before {
  opacity: 1;
  -webkit-transform: scale(2);
          transform: scale(2);
}
.ico_creative_btn:hover [class*=dot_] {
  border-color: var(--bs-white);
}
.ico_creative_btn:hover [class*=icon_arrow_] path {
  stroke: var(--bs-white);
}
.ico_creative_btn:hover .icon_arrow_left path:nth-child(1) {
  -webkit-transform: translate(-6px, 4px);
          transform: translate(-6px, 4px);
}
.ico_creative_btn:hover .icon_arrow_left path:nth-child(2) {
  -webkit-transform: translateX(-6px);
          transform: translateX(-6px);
}
.ico_creative_btn:hover .icon_arrow_left path:nth-child(3) {
  -webkit-transform: translate(-6px, -4px);
          transform: translate(-6px, -4px);
}
.ico_creative_btn:hover .icon_arrow_right path:nth-child(1) {
  -webkit-transform: translate(6px, -4px);
          transform: translate(6px, -4px);
}
.ico_creative_btn:hover .icon_arrow_right path:nth-child(2) {
  -webkit-transform: translateX(6px);
          transform: translateX(6px);
}
.ico_creative_btn:hover .icon_arrow_right path:nth-child(3) {
  -webkit-transform: translate(6px, 4px);
          transform: translate(6px, 4px);
}

.ico_btn_link {
  gap: 8px;
  line-height: 1;
  font-size: 20px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-bottom: 12px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-primary);
  font-family: var(--bs-heading-font-family);
}
.ico_btn_link:before {
  left: 0;
  bottom: 0;
  content: "";
  width: 100%;
  height: 2px;
  border-radius: 6px;
  position: absolute;
  background-image: -webkit-gradient(linear, left top, right top, from(#FF3BD4), to(#2A246D));
  background-image: linear-gradient(90deg, #FF3BD4, #2A246D);
}
.ico_btn_link .btn_icon {
  width: 25px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.ico_btn_link .icon_arrow {
  overflow: visible;
}
.ico_btn_link .icon_arrow path {
  stroke-width: 2px;
  stroke: var(--bs-primary);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.ico_btn_link:hover .btn_icon {
  -webkit-transform: translateX(3px);
          transform: translateX(3px);
}
.ico_btn_link:hover .btn_icon svg path:nth-child(1) {
  -webkit-transform: translate(3px, -3px);
          transform: translate(3px, -3px);
}
.ico_btn_link:hover .btn_icon svg path:nth-child(2) {
  -webkit-transform: translateX(3px);
          transform: translateX(3px);
}
.ico_btn_link:hover .btn_icon svg path:nth-child(3) {
  -webkit-transform: translate(3px, 3px);
          transform: translate(3px, 3px);
}

@-webkit-keyframes animationScrollDown {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-6px);
            transform: translateY(-6px);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    -webkit-transform: translateY(6px);
            transform: translateY(6px);
  }
}

@keyframes animationScrollDown {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-6px);
            transform: translateY(-6px);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    -webkit-transform: translateY(6px);
            transform: translateY(6px);
  }
}
.scroll_down {
  z-index: 1;
  width: 86px;
  height: 86px;
  font-size: 18px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.scroll_down i {
  display: block;
  margin: -5px 0;
  line-height: 1;
  color: var(--bs-light);
  -webkit-animation: animationScrollDown 2s infinite;
          animation: animationScrollDown 2s infinite;
}
.scroll_down i:nth-child(2) {
  -webkit-animation-delay: -0.2s;
          animation-delay: -0.2s;
}
.scroll_down i:nth-child(3) {
  -webkit-animation-delay: -0.4s;
          animation-delay: -0.4s;
}
.scroll_down .spin_image {
  top: 1px;
  left: 0;
  right: 0;
  z-index: -1;
  opacity: 0.1;
  position: absolute;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  -webkit-animation: spin 10s linear infinite;
          animation: spin 10s linear infinite;
}
.scroll_down:hover .spin_image {
  opacity: 0.25;
}

/* 2.04 - Button - End
================================================== */
/* 2.05 - Video - Start
================================================== */
.video_popup_block {
  z-index: 1;
  width: 70px;
  height: 70px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 100%;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-light);
  border: 5px solid var(--bs-primary);
}
.video_popup_block:before, .video_popup_block:after {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  z-index: -1;
  content: "";
  position: absolute;
  border-radius: 100%;
  -webkit-transition: all 0.33s ease;
  transition: all 0.33s ease;
  border: 1px solid var(--bs-primary);
  -webkit-animation: ripple 1.5s linear infinite;
          animation: ripple 1.5s linear infinite;
}
.video_popup_block:before {
  -webkit-animation-delay: 0.1s;
          animation-delay: 0.1s;
}
.video_popup_block:after {
  -webkit-animation-delay: 0.9s;
          animation-delay: 0.9s;
}
.video_popup_block i {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 36px;
  padding: 0 0 0 8px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: inherit;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: calc(100% - 8px);
  height: calc(100% - 8px);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  background-color: var(--bs-primary);
  text-shadow: 0 2px 0 rgba(0, 0, 0, 0.2);
}

/* 2.05 - Video - End
================================================== */
/* 2.06 - Typography - Start
================================================== */
.ico_heading_block {
  margin-bottom: 52px;
}
.ico_heading_block .highlight_title {
  z-index: 1;
  line-height: 1;
  padding: 0 20px;
  font-size: 16px;
  position: relative;
  display: inline-block;
  text-transform: uppercase;
}
.ico_heading_block .highlight_title:before, .ico_heading_block .highlight_title:after {
  top: 5px;
  content: "";
  width: 8px;
  height: 8px;
  position: absolute;
  border-radius: 100%;
  background-color: var(--bs-light);
  background-image: radial-gradient(#5349CA, transparent);
}
.ico_heading_block .highlight_title:before {
  left: 0;
}
.ico_heading_block .highlight_title:after {
  right: 0;
}
.ico_heading_block .heading_text {
  font-size: 50px;
  line-height: 62px;
  letter-spacing: -0.4px;
}

.memecoin_heading_block {
  margin-bottom: 56px;
}
.memecoin_heading_block .heading_text {
  font-size: 54px;
  font-weight: 400;
  line-height: 64px;
  margin-bottom: 14px;
}
.memecoin_heading_block .heading_description {
  font-size: 18px;
  line-height: 26px;
}

.pepecoin_heading_block {
  margin-bottom: 56px;
}
.pepecoin_heading_block .heading_text {
  line-height: 1;
  font-size: 80px;
  font-weight: 400;
  margin-bottom: 14px;
}
.pepecoin_heading_block .heading_text:has(.shape_dot) {
  gap: 0 30px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.pepecoin_heading_block .heading_text:has(.shape_dot) .shape_dot {
  display: block;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  max-width: 60px;
  margin: -8px 0 0;
}
.pepecoin_heading_block .heading_description {
  font-size: 24px;
  line-height: 34px;
}

/* 2.06 - Typography - End
================================================== */
/* 2.07 - Form - Start
================================================== */
.form-group {
  position: relative;
}
.form-group:not(:last-child) {
  margin-bottom: 15px;
}
.form-group .form-control {
  padding: 0 24px;
  font-size: 16px;
  -webkit-box-shadow: none;
          box-shadow: none;
  position: relative;
  border-radius: 6px;
  -webkit-transition: 0.3s ease;
  transition: 0.3s ease;
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  color: var(--bs-heading-color);
  background-color: var(--bs-light);
  caret-color: var(--bs-heading-color);
  border: 1px solid var(--bs-border-color);
}
.form-group .form-control:focus {
  border-color: var(--bs-secondary);
}
.form-group .form-control::-webkit-input-placeholder {
  opacity: 0.6;
  font-size: 16px;
  color: var(--bs-body-color);
}
.form-group .form-control::-moz-placeholder {
  opacity: 0.6;
  font-size: 16px;
  color: var(--bs-body-color);
}
.form-group .form-control:-ms-input-placeholder {
  opacity: 0.6;
  font-size: 16px;
  color: var(--bs-body-color);
}
.form-group .form-control::-ms-input-placeholder {
  opacity: 0.6;
  font-size: 16px;
  color: var(--bs-body-color);
}
.form-group .form-control::placeholder {
  opacity: 0.6;
  font-size: 16px;
  color: var(--bs-body-color);
}
.form-group .form-control:-ms-input-placeholder {
  opacity: 0.6;
  font-size: 16px;
  color: var(--bs-body-color);
}
.form-group .form-control::-ms-input-placeholder {
  opacity: 0.6;
  font-size: 16px;
  color: var(--bs-body-color);
}
.form-group input.form-control {
  height: 50px;
}
.form-group textarea.form-control {
  min-height: 120px;
  margin-bottom: 34px;
  padding: 15px 25px 25px;
}

input[type=search]::-webkit-search-decoration,
input[type=search]::-webkit-search-cancel-button,
input[type=search]::-webkit-search-results-button,
input[type=search]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}

.search_input {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.search_input input {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  height: 60px;
  padding: 0 24px;
  color: var(--bs-white);
  border-radius: 10px 0 0 10px;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.search_input input:focus {
  border-color: var(--bs-secondary);
}
.search_input input::-webkit-input-placeholder {
  opacity: 0.7;
  color: var(--bs-body-color);
}
.search_input input::-moz-placeholder {
  opacity: 0.7;
  color: var(--bs-body-color);
}
.search_input input:-ms-input-placeholder {
  opacity: 0.7;
  color: var(--bs-body-color);
}
.search_input input::-ms-input-placeholder {
  opacity: 0.7;
  color: var(--bs-body-color);
}
.search_input input::placeholder {
  opacity: 0.7;
  color: var(--bs-body-color);
}
.search_input button {
  width: 60px;
  height: 60px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 0 10px 10px 0;
  background-color: var(--bs-secondary);
}
.search_input button img {
  max-width: 24px;
}

.form-check .form-check-input,
.form-check .form-check-label {
  cursor: pointer;
}

.ico_newsletter_form {
  width: 100%;
  position: relative;
}
.ico_newsletter_form input {
  width: 100%;
  height: 60px;
  line-height: 1;
  color: #3B3B3E;
  padding: 0 20px;
  font-size: 18px;
  border-radius: 6px;
  background-color: #D4D5F1;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  border: 1px solid var(--bs-border-color);
}
.ico_newsletter_form input:focus {
  background-color: var(--bs-white);
}
.ico_newsletter_form input::-webkit-input-placeholder {
  color: #3B3B3E;
}
.ico_newsletter_form input::-moz-placeholder {
  color: #3B3B3E;
}
.ico_newsletter_form input:-ms-input-placeholder {
  color: #3B3B3E;
}
.ico_newsletter_form input::-ms-input-placeholder {
  color: #3B3B3E;
}
.ico_newsletter_form input::placeholder {
  color: #3B3B3E;
}
.ico_newsletter_form input:-ms-input-placeholder {
  color: #3B3B3E;
}
.ico_newsletter_form input::-ms-input-placeholder {
  color: #3B3B3E;
}
.ico_newsletter_form .submit_btn {
  top: 4px;
  right: 4px;
  z-index: 1;
  line-height: 1;
  font-size: 18px;
  border-radius: 6px;
  position: absolute;
  padding: 17px 22px;
  color: var(--bs-white);
  background-color: var(--bs-border-color);
  font-family: var(--bs-heading-font-family);
}
.ico_newsletter_form .submit_btn:hover {
  background-color: var(--bs-secondary);
}

.language_dropdown > button {
  gap: 6px;
  line-height: 1;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.language_dropdown > button span {
  width: 26px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.language_dropdown > button i {
  font-size: 16px;
}

.language_dropdown {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
}

.language_dropdown .language_dropdown {
  left: 0;
  top: 100%;
  z-index: 1;
  padding: 6px;
  display: none;
  margin-top: 16px;
  position: absolute;
  border-radius: 6px;
  background-color: var(--bs-white);
  -webkit-box-shadow: 0 30px 50px rgba(0, 0, 0, 0.6);
          box-shadow: 0 30px 50px rgba(0, 0, 0, 0.6);
}
.language_dropdown .language_dropdown:before {
  left: 0;
  right: 0;
  top: -16px;
  content: "";
  height: 16px;
  position: absolute;
}
.language_dropdown .language_dropdown > ul {
  width: 180px;
  display: block;
  max-height: 240px;
  overflow-y: scroll;
}

.language_dropdown:hover .language_dropdown {
  display: block;
}

.language_dropdown .language_dropdown li {
  gap: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  line-height: 1;
  font-size: 16px;
  font-weight: 500;
  padding: 6px 10px;
  border-radius: 6px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: var(--bs-dark);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}

.language_dropdown .language_dropdown li:hover {
  cursor: pointer;
  background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.language_dropdown .language_dropdown li img {
  -webkit-box-ordinal-group: 0;
      -ms-flex-order: -1;
          order: -1;
  width: 28px;
}

.language_dropdown ::-webkit-scrollbar {
  width: 4px;
  height: 10px;
}
.language_dropdown ::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: rgba(var(--bs-dark-rgb), 0.4);
}

/* 2.07 - Form - End
================================================== */
/* 2.08 - Social - Start
================================================== */
.social_block {
  gap: 10px;
}
.social_block a {
  width: 52px;
  height: 52px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 18px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 100%;
  color: var(--bs-white);
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}
.social_block a svg {
  width: 18px;
  height: auto;
  fill: var(--bs-white);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.social_block a i {
  display: block;
  line-height: 1;
}
.social_block a:hover {
  color: var(--bs-dark);
  border-color: var(--bs-white);
  background-color: var(--bs-white);
}
.social_block a:hover svg {
  fill: var(--bs-dark);
}

.social_block.style_2 a {
  z-index: 1;
  border: none;
  font-size: 20px;
  overflow: hidden;
  border-radius: 6px;
  position: relative;
  background: linear-gradient(60deg, #DD00AC, #7130C3, #DD00AC);
}
.social_block.style_2 a:before {
  inset: 1px;
  content: "";
  z-index: -1;
  border-radius: 6px;
  position: absolute;
  background-color: var(--bs-light);
}
.social_block.style_2 a:after {
  left: 0;
  top: -60%;
  content: "";
  width: 100%;
  height: 100%;
  -webkit-filter: blur(10px);
          filter: blur(10px);
  position: absolute;
  background-image: radial-gradient(#4E47A6, transparent);
}
.social_block.style_2 a:hover {
  color: var(--bs-white);
  -webkit-transform: translateY(-2px);
          transform: translateY(-2px);
  -webkit-box-shadow: 0 6px 20px 0 rgba(var(--bs-primary-rgb), 0.3);
          box-shadow: 0 6px 20px 0 rgba(var(--bs-primary-rgb), 0.3);
}
.social_block.style_2 a:hover svg {
  fill: var(--bs-white);
}
.social_block.style_2 svg {
  width: 20px;
}

.social_block.style_3 a {
  width: 60px;
  height: 60px;
  border: none;
  font-size: 20px;
  background-size: cover;
  background-repeat: no-repeat;
  background-color: transparent;
  background-position: center center;
}
.social_block.style_3 a svg {
  width: 20px;
}
.social_block.style_3 a:hover {
  color: var(--bs-primary);
  background-color: var(--bs-primary);
}
.social_block.style_3 a:hover svg {
  fill: var(--bs-primary);
}

.social_block.style_4 a {
  width: 40px;
  height: 40px;
  font-size: 14px;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.social_block.style_4 a:hover {
  color: var(--bs-white);
  border-color: var(--bs-secondary);
  background-color: var(--bs-secondary);
}

/* 2.08 - Social - End
================================================== */
/* 2.09 - Pagelist - Start
================================================== */
.pagelist_block.unordered_list {
  gap: 16px 50px;
}
.pagelist_block a {
  line-height: 1;
  font-size: 16px;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}
.pagelist_block a:hover {
  color: var(--bs-primary);
}

/* 2.09 - Pagelist - End
================================================== */
/* 2.10 - Iconlist - Start
================================================== */
.iconlist_block.unordered_list_block {
  gap: 20px;
}
.iconlist_block.unordered_list_block > li {
  gap: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}
.iconlist_block.unordered_list_block > li > * {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.iconlist_block.unordered_list {
  gap: 26px;
}
.iconlist_block .iconlist_icon {
  width: 22px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  margin: 4px 0 0;
}
.iconlist_block .iconlist_icon .fa-circle {
  font-size: 5px;
}
.iconlist_block .iconlist_icon img {
  width: 100%;
}
.iconlist_block .iconlist_label {
  gap: 6px;
  font-size: 20px;
  font-weight: 700;
  line-height: 28px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}
.iconlist_block .iconlist_info {
  font-weight: 400;
  font-size: var(--bs-body-font-size);
  line-height: var(--bs-body-line-height);
  font-family: var(--bs-body-font-family);
}

/* 2.10 - Iconlist - End
================================================== */
/* 2.11 - Tab - Start
================================================== */
.tab_block .nav {
  gap: 2px;
  padding: 8px;
  -ms-grid-row-align: center;
  -ms-grid-column-align: center;
  place-self: center;
  border-radius: 10px;
  margin-bottom: 70px;
  border: 1px solid var(--bs-border-color);
}
.tab_block .nav .nav-link {
  font-size: 16px;
  padding: 6px 30px;
  line-height: 30px;
  border-radius: 6px;
  letter-spacing: -0.2px;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}
.tab_block .nav .nav-link:not(.active):hover {
  color: var(--bs-white);
}
.tab_block .nav .nav-link.active {
  color: var(--bs-white);
  background-color: var(--bs-border-color);
}

/* 2.11 - Tab - End
================================================== */
/* 2.12 - Iconbox - Start
================================================== */
.ico_iconbox_block {
  z-index: 1;
  padding: 30px;
  position: relative;
  border-radius: 20px;
  background-image: -webkit-gradient(linear, left bottom, left top, from(transparent), to(var(--bs-border-color)));
  background-image: linear-gradient(0deg, transparent, var(--bs-border-color));
}
.ico_iconbox_block:before {
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  z-index: -1;
  content: "";
  position: absolute;
  border-radius: inherit;
  background-color: var(--bs-dark);
}
.ico_iconbox_block .iconbox_icon {
  width: 80px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.ico_iconbox_block .iconbox_icon img {
  display: block;
}
.ico_iconbox_block .iconbox_title {
  font-size: 24px;
  line-height: 32px;
  margin: 27px 0 7px;
}
.ico_iconbox_block .iconbox_description {
  max-width: 235px;
}

.ico_iconbox_icon_left {
  gap: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 30px 20px;
  border-radius: 10px;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.ico_iconbox_icon_left .iconbox_icon {
  width: 55px;
  height: 55px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}
.ico_iconbox_icon_left .iconbox_icon img {
  width: 100%;
}
.ico_iconbox_icon_left .iconbox_title {
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;
  margin-bottom: 4px;
  color: var(--bs-body-color);
  font-family: var(--bs-body-font-family);
}
.ico_iconbox_icon_left .iconbox_description {
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}

.ico_features_group {
  gap: 20px;
}

.memecoin_iconbox_block .iconbox_icon {
  z-index: 1;
  max-width: 90px;
  position: relative;
  display: inline-block;
}
.memecoin_iconbox_block .iconbox_icon:before {
  left: 50%;
  content: "";
  z-index: -1;
  bottom: 8px;
  width: 70px;
  height: 70px;
  position: absolute;
  border-radius: 100%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  border: 2px solid var(--bs-dark);
  background-image: -webkit-gradient(linear, left bottom, left top, from(#07B175), to(#3FFFB7));
  background-image: linear-gradient(0deg, #07B175, #3FFFB7);
}
.memecoin_iconbox_block .iconbox_title {
  font-size: 18px;
  line-height: 24px;
  margin: 28px 0 10px;
}

.row:has(.memecoin_iconbox_block) {
  border-style: dashed;
  border-width: 0 1px;
  border-color: #C0DBA6;
}
.row:has(.memecoin_iconbox_block) > *:not(:last-child) {
  border-style: dashed;
  border-color: #C0DBA6;
  border-width: 0 1px 0 0;
}
.row:has(.memecoin_iconbox_block) .memecoin_iconbox_block {
  margin: 0 auto;
  padding: 0 20px;
  max-width: 320px;
}

.pepecoin_feature_block {
  position: relative;
  text-align: center;
  padding: 106px 80px 94px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center center;
}
.pepecoin_feature_block .feature_serial_number {
  top: -20px;
  left: -30px;
  z-index: 1;
  width: 184px;
  height: 96px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  line-height: 1;
  font-size: 44px;
  font-weight: 500;
  position: absolute;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-size: 100% 100%;
  font-family: "January Night";
  background-repeat: no-repeat;
  background-position: center center;
}
.pepecoin_feature_block .feature_title {
  line-height: 1;
  font-size: 30px;
  margin-bottom: 14px;
}
.pepecoin_feature_block p {
  font-size: 18px;
  font-weight: 600;
  line-height: 30px;
}

.row:has(.pepecoin_feature_block) {
  margin: -40px;
}
.row:has(.pepecoin_feature_block) > * {
  padding: 40px;
}

/* 2.12 - Iconbox - End
================================================== */
/* 2.13 - Event - Start
================================================== */
.ico_event_section [class*=shape_shadow_] {
  width: 252px;
  -webkit-filter: blur(100px);
          filter: blur(100px);
}
.ico_event_section .shape_shadow_1 {
  top: 50%;
  left: -100px;
}
.ico_event_section .shape_shadow_2 {
  top: 110%;
  right: -100px;
}

.event_loop_builder:has(.event_block_left_image) {
  gap: 20px;
}

.event_block_left_image {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 15px;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.event_block_left_image .event_image {
  display: block;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  overflow: hidden;
  max-width: 249px;
  position: relative;
  background: -webkit-gradient(linear, left bottom, left top, from(#7130C3), to(#FC6500));
  background: linear-gradient(0deg, #7130C3, #FC6500);
}
.event_block_left_image .event_image img {
  mix-blend-mode: luminosity;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.event_block_left_image .event_image .event_date {
  left: 0;
  right: 0;
  top: 50%;
  z-index: 2;
  opacity: 0;
  line-height: 1;
  font-size: 24px;
  padding: 0 15px;
  position: absolute;
  text-align: center;
  color: var(--bs-white);
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  font-family: var(--bs-heading-font-family);
}
.event_block_left_image .event_info {
  padding: 30px;
}
.event_block_left_image .event_location {
  line-height: 1;
  font-size: 16px;
  margin-bottom: 16px;
}
.event_block_left_image .event_location i {
  margin-right: 4px;
}
.event_block_left_image .event_title {
  font-size: 20px;
  line-height: 28px;
}
.event_block_left_image .event_title:hover a {
  color: var(--bs-white);
}
.event_block_left_image:hover .event_image img {
  opacity: 0.2;
}
.event_block_left_image:hover .event_image .event_date {
  opacity: 1;
}

.event_card_block {
  overflow: hidden;
  position: relative;
  border-radius: 15px;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.event_card_block .event_image {
  display: block;
  overflow: hidden;
}
.event_card_block .event_image img {
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.event_card_block .event_image:hover img {
  -webkit-transform: scale(1.09);
          transform: scale(1.09);
}
.event_card_block .event_date {
  gap: 14px;
  z-index: 2;
  right: 1px;
  width: 25%;
  bottom: -1px;
  height: 162px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0 30px;
  text-align: center;
  position: absolute;
  border-style: solid;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-width: 0 0 0 1px;
  border-color: var(--bs-secondary);
}
.event_card_block .event_date i {
  width: 42px;
  height: 42px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  border-radius: 100%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-border-color);
}
.event_card_block .event_date span {
  font-size: 20px;
  line-height: 1.2;
  letter-spacing: -0.4px;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}
.event_card_block .event_info {
  width: 75%;
  z-index: 1;
  margin: -87px 0 0;
  position: relative;
  border-style: solid;
  border-radius: 0 15px 0 0;
  border-width: 1px 1px 0 0;
  padding: 30px 40px 40px 30px;
  background-color: var(--bs-light);
  border-color: var(--bs-border-color);
}
.event_card_block .event_location {
  line-height: 1;
  font-size: 16px;
  margin-bottom: 16px;
}
.event_card_block .event_location i {
  margin-right: 4px;
}
.event_card_block .event_title {
  font-size: 28px;
  line-height: 36px;
  margin-bottom: 37px;
}
.event_card_block .event_title:hover a {
  color: var(--bs-white);
}

/* 2.13 - Event - End
================================================== */
/* 2.14 - Roadmap - Start
================================================== */
.roadmap_block {
  z-index: 2;
  padding: 40px;
  position: relative;
  border-radius: 20px;
  -webkit-transition: -webkit-box-flex 0.2s ease-in;
  transition: -webkit-box-flex 0.2s ease-in;
  transition: flex 0.2s ease-in;
  transition: flex 0.2s ease-in, -webkit-box-flex 0.2s ease-in, -ms-flex 0.2s ease-in;
  background-image: -webkit-gradient(linear, left bottom, left top, from(transparent), to(var(--bs-border-color)));
  background-image: linear-gradient(0deg, transparent, var(--bs-border-color));
}
.roadmap_block:before {
  inset: 20px;
  content: "";
  z-index: -1;
  position: absolute;
  border-radius: inherit;
  border: 1px solid transparent;
}
.roadmap_block:after {
  inset: 1px;
  content: "";
  z-index: -2;
  position: absolute;
  border-radius: inherit;
  background-size: 100% 0%;
  background-repeat: no-repeat;
  background-position: center top;
  background-color: var(--bs-dark);
  background-image: url("../images/backgrounds/bg_image_1.png");
}
.roadmap_block .badge {
  line-height: 1;
  font-size: 24px;
  padding: 14px 15px;
  border-radius: 8px;
  border: 1px solid #1D204B;
  color: var(--bs-secondary);
  background-color: rgba(29, 32, 75, 0.41);
  font-family: var(--bs-heading-font-family);
}
.roadmap_block .heading_text {
  font-size: 24px;
  line-height: 32px;
  margin: 31px -20px 18px 0;
}
.roadmap_block .iconlist_block {
  gap: 16px;
  opacity: 0;
  min-height: 350px;
}
.roadmap_block .iconlist_block .iconlist_icon {
  width: auto;
  font-size: 5px;
  margin: 9px 0 0;
}
.roadmap_block .iconlist_block .iconlist_label {
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  color: var(--bs-body-color);
  font-family: var(--bs-body-font-family);
}
.roadmap_block .hover_shape {
  top: 70%;
  right: 30px;
  max-width: 142px;
  position: absolute;
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
  mix-blend-mode: luminosity;
  -webkit-transition: 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  transition: 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}
.roadmap_block.active {
  background-image: -webkit-gradient(linear, left bottom, left top, from(var(--bs-border-color)), to(var(--bs-border-color)));
  background-image: linear-gradient(0deg, var(--bs-border-color), var(--bs-border-color));
}
.roadmap_block.active:before {
  border-color: var(--bs-border-color);
}
.roadmap_block.active:after {
  background-size: 100% 100%;
}
.roadmap_block.active .iconlist_block {
  opacity: 1;
}
.roadmap_block.active .hover_shape {
  top: -13%;
  right: -65px;
  -webkit-transform: scale(1);
          transform: scale(1);
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
  mix-blend-mode: normal;
}

.ico_roadmap_flexbox {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.ico_roadmap_flexbox .roadmap_block {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
  position: relative;
}
.ico_roadmap_flexbox .roadmap_block.active {
  -webkit-box-flex: 1.6;
      -ms-flex: 1.6;
          flex: 1.6;
  z-index: 3;
}

.meme_roadmap_block .serial_number {
  z-index: 2;
  line-height: 1;
  padding: 0 24px;
  font-size: 14px;
  position: relative;
  margin-bottom: 46px;
  color: var(--bs-light);
  font-family: var(--bs-heading-font-family);
}
.meme_roadmap_block .serial_number:before {
  top: 100%;
  left: 60px;
  width: 1px;
  content: "";
  height: 100px;
  margin-top: 10px;
  position: absolute;
  background-color: #1F453D;
}
.meme_roadmap_block .roadmap_info {
  z-index: 1;
  padding: 67px 44px;
  position: relative;
  border-radius: 20px;
  background-color: var(--bs-primary);
}
.meme_roadmap_block .roadmap_info:before {
  inset: 1px;
  content: "";
  z-index: -1;
  position: absolute;
  border-radius: inherit;
  background-image: -webkit-gradient(linear, left bottom, left top, color-stop(40%, var(--bs-dark)), to(transparent));
  background-image: linear-gradient(0deg, var(--bs-dark) 40%, transparent 100%);
}
.meme_roadmap_block .roadmap_info:after {
  top: 50%;
  left: 50%;
  z-index: -1;
  content: "";
  width: 100%;
  height: 250px;
  position: absolute;
  -webkit-filter: blur(50px);
          filter: blur(50px);
  border-radius: 100%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  background-color: var(--bs-dark);
}
.meme_roadmap_block .roadmap_title {
  line-height: 1;
  font-size: 20px;
  position: relative;
  padding-left: 50px;
  margin-bottom: 40px;
}
.meme_roadmap_block .roadmap_title:before {
  top: 2px;
  left: 8px;
  z-index: 2;
  content: "";
  width: 15px;
  height: 15px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  position: absolute;
  border-radius: 100%;
  -webkit-box-shadow: 0 0 0 8px #20242C;
          box-shadow: 0 0 0 8px #20242C;
  background-image: -webkit-gradient(linear, left bottom, left top, from(var(--bs-primary)), to(var(--bs-dark)));
  background-image: linear-gradient(0deg, var(--bs-primary), var(--bs-dark));
}
.meme_roadmap_block .iconlist_block > li {
  gap: 8px;
}
.meme_roadmap_block .iconlist_block .iconlist_icon {
  font-size: 15px;
  margin-top: 2px;
}
.meme_roadmap_block .iconlist_block .iconlist_label {
  font-size: 16px;
  font-weight: 300;
  line-height: 20px;
  font-family: var(--bs-body-font-family);
}

/* 2.14 - Roadmap - End
================================================== */
/* 2.15 - Countdown - Start
================================================== */
.countdown_timer_block {
  gap: 20px;
  text-align: center;
}
.countdown_timer_block > li {
  gap: 8px;
  width: 110px;
  height: 90px;
  line-height: 1;
  padding: 2px 0 0;
  position: relative;
  border-radius: 10px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: reverse;
      -ms-flex-direction: column-reverse;
          flex-direction: column-reverse;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.countdown_timer_block > li:not(:last-child):after {
  top: 50%;
  right: -16px;
  content: ":";
  font-size: 36px;
  font-weight: 700;
  margin-top: -2px;
  line-height: 20px;
  position: absolute;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.countdown_timer_block > li > * {
  display: block;
}
.countdown_timer_block span {
  font-size: 35px;
  font-weight: 700;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}
.countdown_timer_block small {
  font-size: 14px;
  font-weight: 500;
}

/* 2.15 - Countdown - End
================================================== */
/* 2.16 - Accordion - Start
================================================== */
.ico_accordion .accordion-item {
  position: relative;
  border-radius: 10px;
  padding: 35px 120px 35px 40px;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}
.ico_accordion .accordion-item:not(:last-child) {
  margin-bottom: -1px;
}
.ico_accordion .accordion-item:has([aria-expanded=true]) {
  z-index: 2;
}
.ico_accordion .icon_arrow {
  top: 0;
  right: 0;
  bottom: 0;
  width: 100px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  position: absolute;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-left: 1px solid var(--bs-border-color);
}
.ico_accordion .icon_arrow svg {
  width: 26px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  fill: transparent;
  stroke: var(--bs-body-color);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.ico_accordion .icon_arrow:hover svg {
  stroke: var(--bs-secondary);
}
.ico_accordion .icon_arrow:not(.collapsed) svg {
  -webkit-transform: scaleY(-1);
          transform: scaleY(-1);
  fill: var(--bs-secondary);
  stroke: var(--bs-secondary);
}
.ico_accordion .accordion-body {
  padding: 20px 100px 7px 0;
}
.ico_accordion p {
  margin-bottom: 16px;
}
.ico_accordion .accordion-button {
  font-size: 20px;
  line-height: 30px;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}
.ico_accordion .iconlist_block .iconlist_label {
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  color: var(--bs-body-color);
  font-family: var(--bs-body-font-family);
}
.ico_accordion .iconlist_block .iconlist_icon {
  width: auto;
  font-size: 5px;
  margin: 10px 0 0;
}
.ico_accordion .iconlist_block.unordered_list_block {
  gap: 12px;
}

.meme_accordion .accordion-item {
  z-index: 1;
  position: relative;
  border-radius: 20px;
  -webkit-transition: 0.2s ease-in-out;
  transition: 0.2s ease-in-out;
  padding: 60px 80px 60px 40px;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-primary);
}
.meme_accordion .accordion-item:not(:last-child) {
  margin-bottom: 30px;
}
.meme_accordion .accordion-item:before {
  top: 50%;
  left: -1px;
  right: -1px;
  content: "";
  height: 100%;
  z-index: -1;
  position: absolute;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  background-image: -webkit-gradient(linear, left bottom, left top, from(transparent), color-stop(var(--bs-dark)), color-stop(var(--bs-dark)), to(transparent));
  background-image: linear-gradient(0deg, transparent, var(--bs-dark), var(--bs-dark), transparent);
}
.meme_accordion .accordion-item:has([aria-expanded=true]) {
  border-color: var(--bs-light);
  background-color: var(--bs-light);
}
.meme_accordion .accordion-item:has([aria-expanded=true]):before {
  height: 0%;
}
.meme_accordion .accordion-item:has([aria-expanded=true]) .accordion-button {
  color: var(--bs-dark);
}
.meme_accordion .accordion-item:has([aria-expanded=true]) .accordion-button .icon {
  background-color: var(--bs-dark);
}
.meme_accordion .accordion-item:has([aria-expanded=true]) .accordion-button .icon i {
  color: var(--bs-light);
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.meme_accordion .accordion-button {
  gap: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 20px;
  line-height: 28px;
  color: var(--bs-white);
  -webkit-transition: 0.1s ease-in-out;
  transition: 0.1s ease-in-out;
  font-family: var(--bs-heading-font-family);
}
.meme_accordion .accordion-button .icon {
  width: 50px;
  height: 50px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  font-size: 36px;
  border-radius: 10px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  background-color: var(--bs-light);
}
.meme_accordion .accordion-button .icon i {
  color: var(--bs-dark);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.meme_accordion .accordion-body {
  padding: 30px 0 0;
}
.meme_accordion p {
  font-size: 18px;
  line-height: 26px;
  color: var(--bs-dark);
}

/* 2.16 - Accordion - End
================================================== */
/* 2.17 - ProgressBar - Start
================================================== */
.progress {
  height: 22px;
  padding: 2px;
  border-radius: 30px;
  background-color: var(--bs-light);
}
.progress [aria-valuenow="80"] {
  width: 80%;
}
.progress [aria-valuenow="85"] {
  width: 85%;
}
.progress [aria-valuenow="90"] {
  width: 90%;
}
.progress [aria-valuenow="60"] {
  width: 60%;
}
.progress [aria-valuenow="94"] {
  width: 94%;
}

.progress-bar {
  position: relative;
  border-radius: inherit;
  color: var(--bs-heading-color);
  background-color: var(--bs-primary);
}
.progress-bar:after {
  top: 2px;
  right: 10px;
  line-height: 1;
  font-size: 15px;
  font-weight: 500;
  position: absolute;
  content: attr(aria-valuenow) "%";
}

/* 2.17 - ProgressBar - End
================================================== */
/* 2.18 - Authorbox - Start
================================================== */
.postabmin_block {
  gap: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 40px;
  border-radius: 20px;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.postabmin_block .admin_image {
  width: 150px;
  height: 150px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  overflow: hidden;
  border-radius: 100%;
}
.postabmin_block .admin_name {
  line-height: 1;
  font-size: 22px;
  margin-bottom: 4px;
}
.postabmin_block .admin_designation {
  display: block;
  margin-bottom: 10px;
}
.postabmin_block .social_block.style_3 a {
  width: 40px;
  height: 40px;
  font-size: 16px;
  border: 1px solid var(--bs-border-color);
}
.postabmin_block .social_block.style_3 a:hover {
  color: var(--bs-white);
  border-color: var(--bs-primary);
}

/* 2.18 - Authorbox - End
================================================== */
/* 2.19 - Pagination - Start
================================================== */
.pagination_nav {
  gap: 14px;
}
.pagination_nav a {
  width: 50px;
  height: 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 18px;
  font-weight: 700;
  border-radius: 6px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: var(--bs-white);
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.pagination_nav > li:hover a,
.pagination_nav > li.active a {
  color: var(--bs-white);
  border-color: var(--bs-secondary);
  background-color: var(--bs-secondary);
}

/* 2.19 - Pagination - End
================================================== */
/* 2.20 - Team - Start
================================================== */
.ico_team_block {
  height: 336px;
  text-align: center;
  -webkit-perspective: 1000px;
          perspective: 1000px;
}
.ico_team_block .ico_team_block_inner {
  width: 100%;
  height: 100%;
  position: relative;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
  background-color: var(--bs-dark);
  -webkit-transition: 0.6s cubic-bezier(0.33, 1, 0.68, 1);
  transition: 0.6s cubic-bezier(0.33, 1, 0.68, 1);
}
.ico_team_block:hover .ico_team_block_inner {
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
}
.ico_team_block .front_side_content,
.ico_team_block .back_side_content {
  width: 100%;
  height: 100%;
  padding: 40px;
  position: absolute;
  border-radius: 15px;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  border: 1px solid var(--bs-border-color);
}
.ico_team_block .back_side_content {
  overflow: hidden;
  position: relative;
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
  background-color: var(--bs-light);
}
.ico_team_block .back_side_content:before {
  top: -50%;
  left: 50%;
  z-index: -1;
  content: "";
  width: 270px;
  height: 270px;
  -webkit-filter: blur(30px);
          filter: blur(30px);
  position: absolute;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background-image: radial-gradient(#4E47A6, transparent);
}
.ico_team_block .team_avatar {
  padding: 6px;
  border-radius: 100%;
  margin-bottom: 26px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: var(--bs-secondary);
}
.ico_team_block .team_avatar .avatar_wrap {
  width: 164px;
  height: 164px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: inherit;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.ico_team_block .team_member_name {
  line-height: 1;
  font-size: 22px;
  margin-bottom: 10px;
  letter-spacing: -0.2px;
}
.ico_team_block .team_member_designation {
  line-height: 1;
  display: block;
  font-size: 14px;
}
.ico_team_block .team_member_description {
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 23px;
  color: var(--bs-white);
}
.ico_team_block .social_block {
  margin-bottom: 58px;
}

/* 2.20 - Team - End
================================================== */
/* 2.21 - Partnerlogo - Start
================================================== */
.partner_logo_carousel .swiper-wrapper {
  pointer-events: none;
  -webkit-transition-timing-function: linear;
          transition-timing-function: linear;
}

.partners_group {
  gap: 30px;
}
.partners_group .ico_partner_logo .logo_wrap {
  height: 86px;
}
.partners_group .ico_partner_logo .logo_wrap img {
  max-width: 130px;
}

.ico_partner_logo {
  z-index: 1;
  padding: 1px;
  pointer-events: auto;
  position: relative;
  border-radius: 10px;
  background-color: rgba(47, 51, 109, 0.8);
}
.ico_partner_logo:before {
  inset: 0;
  opacity: 0;
  z-index: -1;
  content: "";
  position: absolute;
  border-radius: inherit;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  background-image: linear-gradient(120deg, #944FEB 0%, #DB5D41 37%, #944FEB 100%);
}
.ico_partner_logo:after {
  left: -1px;
  right: -1px;
  top: 50%;
  content: "";
  height: 55%;
  position: absolute;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  background-color: var(--bs-dark);
}
.ico_partner_logo .logo_wrap {
  z-index: 1;
  height: 128px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0 36px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: inherit;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-dark);
}
.ico_partner_logo .logo_wrap img {
  opacity: 0.6;
  max-width: 150px;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.ico_partner_logo .logo_wrap [class*=dot_] {
  z-index: 1;
  width: 5px;
  height: 5px;
  position: absolute;
  border-radius: 100%;
  display: inline-block;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  background: rgba(47, 51, 109, 0.8);
}
.ico_partner_logo .logo_wrap .dot_1 {
  top: 10px;
  left: 10px;
}
.ico_partner_logo .logo_wrap .dot_2 {
  top: 10px;
  right: 10px;
}
.ico_partner_logo .logo_wrap .dot_3 {
  right: 10px;
  bottom: 10px;
}
.ico_partner_logo .logo_wrap .dot_4 {
  left: 10px;
  bottom: 10px;
}
.ico_partner_logo:hover:before {
  opacity: 1;
}
.ico_partner_logo:hover .logo_wrap img {
  opacity: 1;
}
.ico_partner_logo:hover .logo_wrap [class*=dot_] {
  background: radial-gradient(#5349CA, rgba(47, 51, 109, 0.8));
}

/* 2.21 - Partnerlogo - End
================================================== */
/* 2.22 - Tokenomics - Start
================================================== */
.tokenomics_block > li {
  gap: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.tokenomics_block > li:not(:last-child) {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--bs-border-color);
}
.tokenomics_block > li:nth-child(1) .percent {
  background-color: #FC1CCF;
}
.tokenomics_block > li:nth-child(2) .percent {
  background-color: #DF5E08;
}
.tokenomics_block > li:nth-child(3) .percent {
  background-color: #07C87F;
}
.tokenomics_block > li:nth-child(4) .percent {
  background-color: #49B7F7;
}
.tokenomics_block > li:nth-child(5) .percent {
  background-color: #DABA5E;
}
.tokenomics_block > li:nth-child(6) .percent {
  background-color: #FF0054;
}
.tokenomics_block .percent {
  width: 78px;
  height: 46px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  line-height: 1;
  font-size: 24px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-white);
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 10px 0 10px 0;
  font-family: "Roobert PRO Medium";
  background-color: var(--bs-secondary);
}
.tokenomics_block .heading_text {
  line-height: 1;
  font-size: 18px;
  margin-bottom: 8px;
  font-family: var(--bs-body-font-family);
}
.tokenomics_block .value {
  line-height: 1;
  font-size: 20px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-heading-color);
  font-family: "Roobert PRO Medium";
}

.icon_coins_image {
  margin: 0 -120px -140px;
}
.icon_coins_image img {
  -webkit-animation: upDownMover 4s ease-in-out infinite;
          animation: upDownMover 4s ease-in-out infinite;
}

/* 2.22 - Tokenomics - End
================================================== */
/* 2.23 - Blog - Start
================================================== */
.blog_carousel_block {
  overflow: hidden;
  position: relative;
  border-radius: 20px;
  margin-bottom: 120px;
}
.blog_carousel_block .swiper-slide {
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-height: 600px;
  position: relative;
  background-size: cover;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  background-repeat: no-repeat;
  background-blend-mode: luminosity;
  background-position: center center;
  background-color: var(--bs-secondary);
}
.blog_carousel_block .swiper-slide:before {
  inset: 0;
  content: "";
  z-index: -1;
  position: absolute;
  background-image: -webkit-gradient(linear, left bottom, left top, from(var(--bs-secondary)), to(transparent));
  background-image: linear-gradient(0deg, var(--bs-secondary), transparent);
}
.blog_carousel_block .post_info {
  padding: 50px;
  max-width: 910px;
}
.blog_carousel_block .badge {
  color: #CCCEEF;
  line-height: 1;
  font-size: 16px;
  padding: 8px 10px;
  border-radius: 5px;
  text-transform: uppercase;
  background-color: var(--bs-dark);
}
.blog_carousel_block .blog_post_title {
  font-size: 48px;
  line-height: 58px;
  margin: 40px 0 21px;
}
.blog_carousel_block .blog_post_title a:hover {
  color: var(--bs-white);
}
.blog_carousel_block .bc-pagination {
  right: 50px;
  bottom: 50px;
  z-index: 2;
  left: auto;
  width: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.blog_carousel_block .bc-pagination .swiper-pagination-bullet {
  opacity: 1;
  width: 10px;
  height: 10px;
  display: block;
  line-height: 1;
  background-color: var(--bs-white);
}
.blog_carousel_block .bc-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: var(--bs-primary);
}
.blog_carousel_block [class*=bc-button-] {
  top: 50%;
  z-index: 2;
  width: 28px;
  height: 158px;
  font-size: 14px;
  position: absolute;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-white);
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-size: 100% 100%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  background-repeat: no-repeat;
  background-position: center center;
}
.blog_carousel_block [class*=bc-button-]:hover {
  color: var(--bs-primary);
}
.blog_carousel_block .bc-button-prev {
  left: -1px;
}
.blog_carousel_block .bc-button-next {
  right: -1px;
}

.blog_post_left_image {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  margin-bottom: 40px;
  border-radius: 20px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.blog_post_left_image .post_image {
  width: 320px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  background-color: var(--bs-secondary);
}
.blog_post_left_image .post_image img {
  mix-blend-mode: luminosity;
}
.blog_post_left_image .post_info {
  padding: 40px;
  max-width: 670px;
}
.blog_post_left_image .blog_post_category {
  line-height: 1;
  font-size: 16px;
  font-weight: 600;
  color: var(--bs-primary);
  text-transform: uppercase;
}
.blog_post_left_image .blog_post_title {
  font-size: 24px;
  line-height: 34px;
  margin: 6px 0 18px;
}
.blog_post_left_image .blog_post_title a:hover {
  color: var(--bs-white);
}
.blog_post_left_image .blog_post_description {
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 30px;
}

.recent_post_list {
  gap: 30px;
}

.recent_post_item {
  gap: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 25px 30px;
  border-radius: 10px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.recent_post_item .blog_post_title {
  font-size: 18px;
  margin-bottom: 0;
  line-height: 24px;
  color: var(--bs-heading-color);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  font-family: var(--bs-heading-font-family);
}
.recent_post_item .blog_post_admin {
  gap: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 16px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.recent_post_item .blog_post_admin i {
  color: var(--bs-primary);
}
.recent_post_item:hover {
  border-color: var(--bs-secondary);
}
.recent_post_item:hover .blog_post_title {
  color: var(--bs-white);
}

.blog_card_block .blog_post_image {
  display: block;
  overflow: hidden;
  margin-bottom: 30px;
  border-radius: 20px;
  background-color: var(--bs-secondary);
}
.blog_card_block .blog_post_image img {
  mix-blend-mode: luminosity;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.blog_card_block .blog_post_title {
  font-size: 24px;
  line-height: 34px;
  margin: 19px 0 25px;
}
.blog_card_block .blog_post_title a:hover {
  color: var(--bs-primary);
}

.row:has(.blog_card_block) {
  margin: -40px -15px;
}
.row:has(.blog_card_block) [class*=col-lg-] {
  padding: 40px 15px;
}

/* 2.23 - Blog - End
================================================== */
/* 3.01 - Site Header - Start
================================================== */
.site_header {
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  position: fixed;
}
.site_header .nav_wrapper {
  padding: 30px 0;
  -webkit-transition: padding 0.3s;
  transition: padding 0.3s;
}
.site_header.sticky .nav_wrapper {
  padding: 10px 0;
  -webkit-backdrop-filter: saturate(180%) blur(20px);
          backdrop-filter: saturate(180%) blur(20px);
}
.site_header .ico_btn_outline {
  font-size: 16px;
  padding: 14px 26px;
}
.site_header .language_dropdown .language_dropdown {
  right: 0;
  left: auto;
}

.header_memecoin .main_menu_list {
  gap: 40px;
}
.header_memecoin .main_menu_list > li > .nav-link {
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0.4px;
}
.header_memecoin .main_menu_list > li > .nav-link > .nav_link_icon {
  margin: 0;
}
.header_memecoin .main_menu_list > li:hover > .nav-link,
.header_memecoin .main_menu_list > li.active > .nav-link {
  color: var(--bs-light);
}
.header_memecoin .main_menu_list > li:hover > .nav-link .nav_link_label {
  -webkit-transform: translateY(-100%);
          transform: translateY(-100%);
}
.header_memecoin .memecoin_btn {
  font-size: 18px;
  padding: 13px 30px;
}

.header_pepecoin.sticky .site_logo .site_link > img:nth-child(1), .header_pepecoin:not(.sticky) .site_logo .site_link > img:nth-child(2) {
  display: none;
}
.header_pepecoin .btns_group {
  margin-left: 70px;
}
.header_pepecoin .pepecoin_btn {
  font-size: 20px;
  padding: 17px 30px;
}
.header_pepecoin:not(.sticky) .main_menu_list > li > .nav-link {
  color: var(--bs-white);
}
.header_pepecoin:not(.sticky) .main_menu_list .nav_link_icon {
  color: var(--bs-white);
}
.header_pepecoin .main_menu_list > li > .nav-link {
  line-height: 1;
  font-size: 18px;
  font-weight: 400;
}
.header_pepecoin .main_menu_list .dropdown-item {
  font-weight: 400;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}
.header_pepecoin .nav_wrapper {
  padding: 60px 0;
}
.header_pepecoin.sticky .nav_wrapper {
  padding: 5px 0;
}

.main_menu_list {
  gap: 46px;
}
.main_menu_list > li > .nav-link {
  gap: 4px;
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  line-height: 1;
  font-size: 16px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  font-weight: 500;
  line-height: 18px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  overflow: hidden;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}
.main_menu_list > li > .nav-link > .nav_link_label {
  padding: 1px 0;
  position: relative;
  display: inline-block;
  -webkit-transition: -webkit-transform 0.3s;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}
.main_menu_list > li > .nav-link > .nav_link_label:before {
  left: 0;
  top: 100%;
  text-align: center;
  position: absolute;
  content: attr(data-text);
}
.main_menu_list > li > .nav-link > .nav_link_icon {
  margin: 1px 0 0;
}
.main_menu_list > li.active > .nav-link {
  color: var(--bs-primary);
}
.main_menu_list > li:hover > .nav-link {
  color: var(--bs-primary);
}
.main_menu_list > li:hover > .nav-link .nav_link_label {
  -webkit-transform: translateY(-90%);
          transform: translateY(-90%);
}
.main_menu_list > li:hover > .nav-link > .nav_link_icon {
  -webkit-transform: rotateX(-180deg);
          transform: rotateX(-180deg);
}
.main_menu_list .nav_link_icon {
  font-size: 16px;
  fill: currentColor;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.main_menu_list .dropdown-menu {
  min-width: 220px;
}
.main_menu_list .dropdown-menu .dropdown > .dropdown-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.main_menu_list .dropdown-menu .dropdown-menu {
  top: 0;
  margin: 0;
  left: 100%;
}

@media screen and (min-width: 992px) {
  .site_header .btns_group {
    gap: 24px;
  }
  .main_menu_list .dropdown-menu {
    opacity: 0;
    display: block;
    -webkit-transition: 200ms;
    transition: 200ms;
    visibility: hidden;
    -webkit-transform-origin: top;
            transform-origin: top;
    -webkit-transform: perspective(300px) rotateX(-8deg);
            transform: perspective(300px) rotateX(-8deg);
  }
  .main_menu_list .dropdown:hover > .dropdown-menu {
    opacity: 1;
    visibility: visible;
    -webkit-transform: perspective(300px) rotateX(0deg);
            transform: perspective(300px) rotateX(0deg);
  }
  .main_menu_list > .dropdown > .dropdown-menu {
    margin-top: 36px;
  }
  .main_menu_list > .dropdown > .dropdown-menu:before {
    left: 0;
    right: 0;
    top: -20px;
    content: "";
    height: 20px;
    display: block;
    position: absolute;
  }
  .site_header.sticky .main_menu_list > .dropdown > .dropdown-menu {
    margin-top: 42px;
  }
  .site_header.sticky .main_menu_list > .dropdown > .dropdown-menu:before {
    top: -22px;
    height: 22px;
  }
}
.mobile_menu_btn {
  width: 44px;
  height: 44px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  font-size: 20px;
  border-radius: 6px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: var(--bs-heading-color);
  border: 1px solid var(--bs-border-color);
}
.mobile_menu_btn:hover {
  color: var(--bs-white);
  border-color: var(--bs-primary);
  background-color: var(--bs-primary);
}

@media screen and (max-width: 1199px) {
  .main_menu_list {
    gap: 40px;
  }
}
@media screen and (max-width: 1024px) {
  .main_menu_list {
    gap: 20px;
  }
  .header_memecoin .main_menu_list {
    gap: 16px;
  }
  .header_pepecoin .nav_wrapper {
    padding: 24px 0;
  }
  .header_pepecoin .pepecoin_btn {
    font-size: 18px;
  }
}
@media screen and (max-width: 991px) {
  .site_header .btns_group {
    gap: 10px;
  }
  .site_header .main_menu {
    left: 0;
    right: 0;
    top: 104px;
    z-index: 999;
    position: fixed;
    padding: 0 15px;
  }
  .site_header.sticky .main_menu {
    top: 64px;
  }
  .main_menu_list {
    gap: 1px;
    margin: auto;
    max-width: 700px;
    border-radius: 6px;
    padding: 15px 30px;
    background-color: var(--bs-white);
    -webkit-box-shadow: 0 5px 20px 0 rgba(0, 0, 0, 0.6);
            box-shadow: 0 5px 20px 0 rgba(0, 0, 0, 0.6);
  }
  .main_menu_list > li:not(:hover) > .nav-link,
  .main_menu_list > li:not(.active) > .nav-link {
    color: var(--bs-dark);
  }
  .main_menu_list > li {
    width: 100%;
    display: block;
    padding: 15px 0;
  }
  .main_menu_list > li:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  .main_menu_list > li > a {
    font-size: 18px;
  }
  .main_menu_list .dropdown-menu {
    position: static;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
  .header_memecoin .main_menu {
    top: 110px;
  }
  .header_memecoin.sticky .main_menu {
    top: 70px;
  }
  .header_memecoin .main_menu_list > li:hover > .nav-link,
  .header_memecoin .main_menu_list > li.active > .nav-link {
    color: var(--bs-primary);
  }
  .header_memecoin .main_menu_list {
    gap: 0;
  }
  .header_pepecoin:not(.sticky) .main_menu_list > li > .nav-link,
  .header_pepecoin:not(.sticky) .main_menu_list .nav_link_icon {
    color: var(--bs-dark);
  }
  .header_pepecoin .main_menu {
    top: 100px;
  }
  .header_pepecoin.sticky .main_menu {
    top: 62px;
  }
}
@media screen and (max-width: 575px) {
  .header_memecoin .memecoin_btn {
    font-size: 14px;
    padding: 10px 24px;
  }
  .header_memecoin .btns_group > li:last-child {
    display: none;
  }
  .header_memecoin .main_menu {
    top: 104px;
  }
  .header_memecoin.sticky .main_menu {
    top: 64px;
  }
}
@media screen and (max-width: 425px) {
  .header_pepecoin .pepecoin_btn {
    font-size: 16px;
    padding: 12px 24px;
  }
  .mobile_menu_btn {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  .header_pepecoin .main_menu {
    top: 88px;
  }
  .header_pepecoin.sticky .main_menu {
    top: 50px;
  }
}
/* 3.01 - Site Header - End
================================================== */
/* 3.02 - Site Footer - Start
================================================== */
.ico_site_footer {
  padding: 146px 0 0;
}
.ico_site_footer .shape_top {
  left: 0;
  right: 0;
  top: -120px;
}
.ico_site_footer .shape_top img {
  width: 100%;
}
.ico_site_footer .pagelist_block {
  margin-bottom: 58px;
}
.ico_site_footer .middle_area {
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-style: solid;
  border-width: 1px 0;
  -webkit-box-align: stretch;
      -ms-flex-align: stretch;
          align-items: stretch;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-color: var(--bs-border-color);
}
.ico_site_footer .middle_area > * {
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 50px 40px;
  text-align: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.ico_site_footer .middle_area > *:not(:first-child, :last-child) {
  width: 40%;
  border-width: 0 1px;
  border-style: solid;
  border-color: var(--bs-border-color);
}
.ico_site_footer .middle_area > *:first-child, .ico_site_footer .middle_area > *:last-child {
  width: 30%;
}
.ico_site_footer .footer_title {
  line-height: 1;
  font-size: 14px;
  margin-bottom: 30px;
}
.ico_site_footer .footer_bottom {
  padding: 37px 0;
}
.ico_site_footer .copyright_text {
  --bs-secondary: #7064E9;
  --bs-secondary-rgb: 112, 100, 233;
}

.meme_site_footer {
  margin-top: -274px;
  padding: 450px 0 30px;
  background-image: -webkit-gradient(linear, left bottom, left top, from(var(--bs-primary)), to(transparent));
  background-image: linear-gradient(0deg, var(--bs-primary), transparent);
}
.meme_site_footer .cartoon_shape_1 {
  left: 0;
  right: 0;
  bottom: 0;
  text-align: center;
}
.meme_site_footer .cartoon_shape_1 img {
  max-width: 1592px;
}
.meme_site_footer .cartoon_shape_2 {
  top: 55%;
  left: 40px;
  max-width: 106px;
}
.meme_site_footer .cartoon_shape_2 img {
  -webkit-animation: upDownMover 3.5s ease-in-out infinite;
          animation: upDownMover 3.5s ease-in-out infinite;
}
.meme_site_footer .cartoon_shape_3 {
  top: 58%;
  right: 40px;
  max-width: 151px;
}
.meme_site_footer .cartoon_shape_3 img {
  -webkit-animation: upDownMover 3.5s ease-in-out infinite 0.8s;
          animation: upDownMover 3.5s ease-in-out infinite 0.8s;
}
.meme_site_footer .footer_heading {
  line-height: 1;
  font-size: 54px;
  margin-bottom: 63px;
}
.meme_site_footer .btns_group {
  margin-bottom: 470px;
}
.meme_site_footer .pagelist_block a {
  font-size: 20px;
}

.pepecoin_site_footer {
  padding: 380px 0 0;
  position: relative;
  background-image: -webkit-gradient(linear, left bottom, left top, from(var(--bs-white)), to(#EFF8CD));
  background-image: linear-gradient(0deg, var(--bs-white), #EFF8CD);
}
.pepecoin_site_footer .shape_sign_board {
  left: 0;
  right: 0;
  bottom: 94px;
  position: absolute;
  text-align: center;
}
.pepecoin_site_footer .shape_sign_board img {
  max-width: 1240px;
  display: inline-block;
}
.pepecoin_site_footer .pepecoin_heading_block {
  margin-bottom: 150px;
}
.pepecoin_site_footer .shape_ground {
  z-index: 1;
  position: relative;
  margin-top: -150px;
}
.pepecoin_site_footer .shape_ground img {
  width: 100%;
}
.pepecoin_site_footer .shape_dragonfly {
  top: 310px;
  left: 150px;
  max-width: 120px;
  position: absolute;
}
.pepecoin_site_footer .shape_dragonfly img {
  -webkit-animation: upDownMover 3s ease-in-out infinite;
          animation: upDownMover 3s ease-in-out infinite;
}
.pepecoin_site_footer .shape_cloud {
  top: 170px;
  right: 70px;
  max-width: 160px;
  position: absolute;
}
.pepecoin_site_footer .shape_cloud img {
  -webkit-animation: upDownMover 3s ease-in-out infinite;
          animation: upDownMover 3s ease-in-out infinite;
}
.pepecoin_site_footer .shape_tree {
  bottom: 24px;
  right: -590px;
  max-width: 1130px;
  position: absolute;
}
.pepecoin_site_footer .copyright_text {
  line-height: 1;
  font-size: 16px;
  font-weight: 700;
}
.pepecoin_site_footer .footer_bottom {
  z-index: 2;
  position: relative;
  padding: 18px 0 16px;
  background-color: #0E2F18;
}

/* 3.02 - Site Footer - End
================================================== */
/* 3.03 - Page Header - Start
================================================== */
.page_header {
  padding: 206px 0 278px;
  background-image: -webkit-gradient(linear, left bottom, left top, from(transparent), to(var(--bs-secondary)));
  background-image: linear-gradient(0deg, transparent, var(--bs-secondary));
}
.page_header + section {
  margin-top: -130px;
}

.page_title {
  font-size: 48px;
  line-height: 60px;
  margin-bottom: 21px;
}

/* 3.03 - Page Header - End
================================================== */
/* 3.04 - Breadcrumb - Start
================================================== */
.breadcrumb {
  margin: 0;
}
.breadcrumb > .breadcrumb-item {
  color: #D4D5F1;
  line-height: 1;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-family: var(--bs-heading-font-family);
}
.breadcrumb > .breadcrumb-item.active {
  color: #D4D5F1;
}
.breadcrumb > .breadcrumb-item a {
  color: #D4D5F1;
}
.breadcrumb > .breadcrumb-item a:hover {
  color: var(--bs-white);
}
.breadcrumb .breadcrumb-item + .breadcrumb-item::before {
  color: inherit;
  content: "\f101";
  font-family: "Font Awesome 5 Pro";
}

/* 3.04 - Breadcrumb - End
================================================== */
/* 3.05 - Sidebar - Start
================================================== */
.sidebar > *:not(:last-child) {
  margin-bottom: 70px;
}

.sidebar_title {
  line-height: 1;
  font-size: 18px;
  margin-bottom: 27px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* 3.05 - Sidebar - End
================================================== */
/* 4.01 - Hero Section - Start
================================================== */
.ico_hero_section {
  overflow: hidden;
  padding: 183px 0 0;
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: center top;
}
.ico_hero_section .hero_title {
  font-size: 70px;
  line-height: 80px;
  margin-bottom: 46px;
}
.ico_hero_section .ico_countdown_progress_box {
  max-width: 880px;
  position: relative;
  margin: 100px auto 16px;
  background-color: var(--bs-dark);
}
.ico_hero_section .ico_countdown_progress_box:before {
  content: "";
  z-index: -2;
  opacity: 0.24;
  position: absolute;
  -webkit-filter: blur(22px);
          filter: blur(22px);
  inset: 6px 0 -8px 0;
  background: linear-gradient(140deg, #7064E9, #FC6500, #7064E9);
}
.ico_hero_section .ico_countdown_progress_box .countdown_timer_block {
  margin: -28px 0 20px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center center;
}
.ico_hero_section .shape_bottom img {
  width: 100%;
}
.ico_hero_section .shape_globe {
  top: 30%;
  left: 62px;
  max-width: 188px;
  -webkit-animation: upDownMover 3.5s ease-in-out infinite;
          animation: upDownMover 3.5s ease-in-out infinite;
}
.ico_hero_section .shape_coin {
  top: 30%;
  right: 98px;
  max-width: 227px;
  -webkit-animation: upDownMover 3.5s ease-in-out infinite 1s;
          animation: upDownMover 3.5s ease-in-out infinite 1s;
}

.ico_hero_section + .partner_section {
  margin-top: -81px;
}

.memecoin_hero_section {
  z-index: 2;
  padding: 220px 0 100px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center bottom;
}
.memecoin_hero_section .hero_title {
  font-size: 80px;
  line-height: 86px;
  margin-bottom: 21px;
}
.memecoin_hero_section .hero_title .shape_image {
  max-width: 80px;
  margin: 0 -14px;
  display: inline-block;
}
.memecoin_hero_section .hero_title .shape_image img {
  display: block;
  margin: 0 0 -10px;
}
.memecoin_hero_section .hero_description {
  font-size: 20px;
  max-width: 598px;
  line-height: 26px;
  margin: 0 auto 74px;
}
.memecoin_hero_section .scrollspy_btn {
  margin: 100px 0 0;
}
.memecoin_hero_section [class*=shape_chain_] img {
  width: 100%;
  height: auto;
}
.memecoin_hero_section .shape_chain_1 {
  top: -130px;
  left: -40px;
  right: -40px;
}
.memecoin_hero_section .shape_chain_2 {
  left: -65px;
  right: -65px;
  bottom: -110px;
}
.memecoin_hero_section [class*=shape_cartoon_] {
  top: 50%;
  max-width: 280px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.memecoin_hero_section [class*=shape_cartoon_] img {
  -webkit-animation: upDownMover 3.5s ease-in-out infinite;
          animation: upDownMover 3.5s ease-in-out infinite;
}
.memecoin_hero_section .shape_cartoon_1 {
  left: 8%;
}
.memecoin_hero_section .shape_cartoon_2 {
  right: 8%;
}
.memecoin_hero_section .shape_cartoon_2 img {
  -webkit-animation-delay: 1s;
          animation-delay: 1s;
}

.pepecoin_hero_section {
  padding: 166px 0 157px;
}
.pepecoin_hero_section .hero_title {
  line-height: 1;
  font-size: 90px;
  letter-spacing: -1px;
  margin: 0 -32px 21px 0;
  color: var(--bs-white);
}
.pepecoin_hero_section .hero_description {
  font-size: 26px;
  line-height: 1.3;
  margin: 0 -20px 51px 0;
  color: var(--bs-white);
}
.pepecoin_hero_section .shape_leaf_top {
  left: 0;
  right: 0;
  top: -80px;
}
.pepecoin_hero_section .shape_leaf_top img {
  width: 100%;
}
.pepecoin_hero_section .shape_leaf_bottom {
  left: -140px;
  right: -20px;
  bottom: -90px;
}
.pepecoin_hero_section .shape_leaf_bottom img {
  width: 100%;
}
.pepecoin_hero_section .shape_cloud {
  top: 170px;
  left: -68px;
  max-width: 186px;
}
.pepecoin_hero_section .shape_cloud img {
  -webkit-animation: upDownMover 3s ease-in-out infinite;
          animation: upDownMover 3s ease-in-out infinite;
}
.pepecoin_hero_section .shape_tree {
  right: -506px;
  bottom: 210px;
  max-width: 720px;
}

.pepecoin_hero_image .pepe_coin_image {
  left: 0;
  right: 0;
  top: 60px;
  text-align: center;
  position: absolute;
}
.pepecoin_hero_image .pepe_coin_image img {
  max-width: 400px;
  -webkit-perspective: 10000px;
          perspective: 10000px;
  -webkit-transform-origin: center;
          transform-origin: center;
  -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
  -webkit-animation: coinFlip 4s infinite linear;
          animation: coinFlip 4s infinite linear;
}

/* 4.01 - Hero Section - End
================================================== */
/* 4.02 - Countdown Section - Start
================================================== */
.ico_countdown_progress_box {
  padding: 15px;
  border-radius: 20px;
  border: 1px solid rgba(var(--bs-border-color-translucent), 0.6);
}
.ico_countdown_progress_box .countdown_timer_block {
  gap: 78px;
  padding: 62px 86px 30px;
}
.ico_countdown_progress_box .countdown_timer_block > li {
  background-color: var(--bs-dark);
  border-color: rgba(var(--bs-border-color-translucent), 0.6);
}
.ico_countdown_progress_box .countdown_timer_block > li:not(:last-child):after {
  margin: 0;
  content: "";
  width: 18px;
  height: 7px;
  right: -49px;
  line-height: 1;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center center;
  background-image: url("../images/shapes/shape_duble_dot.svg");
}
.ico_countdown_progress_box .countdown_timer_block span {
  color: var(--bs-white);
}

.ico_progress {
  border-radius: 10px;
  padding: 34px 18px 32px;
  background-color: var(--bs-light);
  border: 1px solid rgba(47, 51, 109, 0.6);
}

.progress_range_step {
  margin-bottom: 28px;
}
.progress_range_step > li {
  line-height: 1;
  font-size: 16px;
  padding: 0 30px;
  position: relative;
}
.progress_range_step > li:before {
  left: 50%;
  top: 100%;
  width: 2px;
  content: "";
  height: 24px;
  margin-top: 5px;
  position: absolute;
  border-radius: 10px;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background: -webkit-gradient(linear, left bottom, left top, from(transparent), to(var(--bs-primary)));
  background: linear-gradient(0deg, transparent, var(--bs-primary));
}

.progress_value {
  margin-top: 30px;
}
.progress_value > li {
  line-height: 1;
  font-size: 16px;
}

.coin_we_accept {
  margin-bottom: 60px;
}
.coin_we_accept .heading_text {
  line-height: 1;
  font-size: 16px;
  margin-bottom: 30px;
}

.coins_group {
  gap: 22px;
}
.coins_group > li {
  gap: 10px;
  text-align: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.coins_group .coin_icon {
  max-width: 46px;
}
.coins_group .coin_icon img {
  display: block;
}
.coins_group .coin_label {
  line-height: 1;
  font-size: 16px;
  font-weight: 500;
}

.memecoin_countdown_wraper {
  gap: 60px;
  width: 570px;
  height: 570px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  position: relative;
  border-radius: 100%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-dark);
  border: 2px solid var(--bs-dark);
  outline: 4px solid var(--bs-light);
  -webkit-box-shadow: inset 0 0 0 8px var(--bs-primary);
          box-shadow: inset 0 0 0 8px var(--bs-primary);
}
.memecoin_countdown_wraper .shape_circle {
  top: -19px;
  left: -23px;
  right: -19px;
  bottom: -19px;
  z-index: -1;
  position: absolute;
  -webkit-animation: spin 30s linear infinite;
          animation: spin 30s linear infinite;
}
.memecoin_countdown_wraper .shape_circle img {
  width: 100%;
  height: auto;
}
.memecoin_countdown_wraper .shape_cartoon {
  top: 50%;
  left: 52%;
  z-index: -2;
  width: 375px;
  position: absolute;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.memecoin_countdown_wraper .shape_cartoon_hand {
  top: 40%;
  width: 150px;
  right: -112px;
  position: absolute;
}
.memecoin_countdown_wraper .heading_text {
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 20px;
  color: var(--bs-light);
}

.meme_progress {
  width: 420px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}
.meme_progress .progress_value {
  margin-top: 20px;
}
.meme_progress .progress_value > li {
  font-size: 20px;
  font-weight: 700;
}

/* 4.02 - Countdown Section - End
================================================== */
/* 4.03 - About Section - Start
================================================== */
.ico_about_section [class*=shape_shadow_] {
  width: 252px;
  -webkit-filter: blur(100px);
          filter: blur(100px);
}
.ico_about_section .shape_shadow_1 {
  top: 40%;
  left: -80px;
}
.ico_about_section .shape_shadow_2 {
  top: 14%;
  right: -140px;
}

.about_ico_block {
  padding-left: 60px;
}
.about_ico_block > li {
  position: relative;
  padding-bottom: 40px;
  border-bottom: 1px solid rgba(var(--bs-border-color-translucent), 0.31);
}
.about_ico_block > li:not(:last-child) {
  margin-bottom: 40px;
}
.about_ico_block > li:before {
  top: -12px;
  left: -60px;
  line-height: 1;
  font-size: 42px;
  content: "\f192";
  font-weight: 400;
  position: absolute;
  font-family: "Font Awesome 5 Pro";
  background: -webkit-gradient(linear, left bottom, left top, from(#4E47A6), to(#101122));
  background: linear-gradient(0deg, #4E47A6, #101122);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.about_ico_block > li:after {
  top: 30px;
  left: -42px;
  width: 5px;
  content: "";
  bottom: -30px;
  position: absolute;
  background: -webkit-gradient(linear, left bottom, left top, from(#101122), to(#4E47A6));
  background: linear-gradient(0deg, #101122, #4E47A6);
}
.about_ico_block .title_text {
  line-height: 1;
  font-size: 20px;
  margin-bottom: 20px;
}

.ico_about_image {
  position: relative;
}
.ico_about_image .ripple_shape svg path {
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  -webkit-animation: fadeInOut 3s ease-in-out infinite;
          animation: fadeInOut 3s ease-in-out infinite;
}
.ico_about_image .ripple_shape svg path:nth-child(1) {
  -webkit-animation-delay: 0.8s;
          animation-delay: 0.8s;
}
.ico_about_image .ripple_shape svg path:nth-child(2) {
  -webkit-animation-delay: 0.6s;
          animation-delay: 0.6s;
}
.ico_about_image .ripple_shape svg path:nth-child(3) {
  -webkit-animation-delay: 0.4;
          animation-delay: 0.4;
}
.ico_about_image .ripple_shape svg path:nth-child(4) {
  -webkit-animation-delay: 0.2s;
          animation-delay: 0.2s;
}
.ico_about_image .coin_image {
  left: 0;
  right: 0;
  bottom: 12px;
  z-index: 1;
  position: absolute;
}
.ico_about_image .coin_image img {
  -webkit-animation: upDownMover 4s ease-in-out infinite;
          animation: upDownMover 4s ease-in-out infinite;
}

.whitepaper_content {
  overflow: hidden;
  border-radius: 20px;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}

.whitepaper_info_wrap [class*=info_wrap_] {
  padding: 40px 130px 40px 40px;
}
.whitepaper_info_wrap [class*=info_wrap_]:not(:last-child) {
  border-bottom: 1px solid var(--bs-border-color);
}
.whitepaper_info_wrap .heading_text {
  line-height: 1;
  font-size: 24px;
  margin-bottom: 16px;
}
.whitepaper_info_wrap p {
  margin-bottom: 32px;
}
.whitepaper_info_wrap .iconlist_block {
  gap: 14px;
  margin-bottom: 43px;
}
.whitepaper_info_wrap .iconlist_block .iconlist_label {
  font-size: 18px;
  font-weight: 500;
  line-height: 26px;
}

.whitepaper_image_wrap {
  z-index: 1;
  height: 740px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-style: solid;
  background-size: cover;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-width: 0 1px 0 0;
  background-repeat: no-repeat;
  background-color: var(--bs-dark);
  background-position: center center;
  border-color: var(--bs-border-color);
}
.whitepaper_image_wrap:before {
  inset: 0;
  z-index: -1;
  content: "";
  opacity: 0.7;
  position: absolute;
  background-blend-mode: lighten;
  background: -webkit-gradient(linear, left bottom, left top, from(#070710), color-stop(#231740), to(#35225F));
  background: linear-gradient(0deg, #070710, #231740, #35225F);
}
.whitepaper_image_wrap .image_block {
  z-index: 1;
  max-width: 325px;
  position: relative;
  display: inline-block;
  -webkit-animation: upDownMover 3s ease-in-out infinite;
          animation: upDownMover 3s ease-in-out infinite;
}
.whitepaper_image_wrap .image_block:before {
  left: 50%;
  z-index: -1;
  content: "";
  width: 561px;
  height: 110px;
  bottom: -30px;
  opacity: 0.9;
  -webkit-filter: blur(45px);
          filter: blur(45px);
  position: absolute;
  border-radius: 100%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background: -webkit-gradient(linear, left bottom, left top, from(#FB3AD4), color-stop(#5A19AB), to(#010314));
  background: linear-gradient(0deg, #FB3AD4, #5A19AB, #010314);
}

.meme_video_popup {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 20px;
  margin: auto;
  max-width: 400px;
  position: relative;
  border-radius: 24px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  background-color: var(--bs-dark);
  border: 5px solid var(--bs-primary);
}
.meme_video_popup:before {
  top: 100%;
  content: "";
  width: 60px;
  height: 50px;
  right: 100px;
  position: absolute;
  background-color: var(--bs-primary);
  clip-path: polygon(0 0, 100% 0%, 100% 100%);
}
.meme_video_popup .title_text {
  font-size: 30px;
  line-height: 1.1;
}

.meme_popup_video_area .image_block {
  float: right;
  max-width: 665px;
  margin: 80px -100px 0 0;
}

.meme_introducing_image {
  z-index: 1;
  position: relative;
}
.meme_introducing_image .animate_line_image {
  top: -32px;
  right: 56px;
  z-index: -1;
  position: absolute;
  -webkit-animation: smallZoomInOut 2s infinite ease-in-out;
          animation: smallZoomInOut 2s infinite ease-in-out;
}

.meme_process_section {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

.meme_process_heading {
  border-radius: 20px;
  margin-bottom: 30px;
  background-size: cover;
  padding: 55px 70px 62px;
  background-repeat: no-repeat;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-primary);
  background-position: center bottom;
}
.meme_process_heading .cartoon_image {
  z-index: 1;
  max-width: 344px;
  position: relative;
}
.meme_process_heading .cartoon_image .animate_line_image {
  top: 25px;
  left: 85px;
  z-index: -1;
  position: absolute;
  -webkit-animation: smallZoomInOut 2s infinite ease-in-out;
          animation: smallZoomInOut 2s infinite ease-in-out;
}

.meme_progress_block {
  overflow: hidden;
  position: relative;
  border-radius: 20px;
  padding: 36px 50px 60px 30px;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-primary);
}
.meme_progress_block:before {
  content: "";
  right: -46px;
  bottom: -100px;
  width: 130px;
  height: 170px;
  position: absolute;
  border-radius: 10px;
  -webkit-transform: rotate(10deg);
          transform: rotate(10deg);
  border: 1px solid var(--bs-primary);
}
.meme_progress_block .block_title {
  gap: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 18px;
  line-height: 36px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 16px;
}
.meme_progress_block .block_title .icon {
  width: 25px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}
.meme_progress_block .block_title .icon img {
  width: 100%;
}
.meme_progress_block .serial_number {
  z-index: 1;
  right: 10px;
  bottom: 10px;
  width: 46px;
  height: 46px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.meme_progress_block .crown {
  top: 0;
  left: 50%;
  max-width: 16px;
  position: absolute;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.meme_progress_block .number {
  margin-top: 2px;
  color: var(--bs-dark);
  font-family: var(--bs-heading-font-family);
}
.meme_progress_block .spin_bg {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -2;
  position: absolute;
}
.meme_progress_block .spin_bg img {
  -webkit-animation: spin 8s linear infinite;
          animation: spin 8s linear infinite;
}

.pepecoin_about_section .shape_tree {
  bottom: 0;
  left: -604px;
  max-width: 1192px;
}
.pepecoin_about_section .shape_cloud {
  top: 22%;
  left: 15%;
  max-width: 270px;
}
.pepecoin_about_section .shape_cloud img {
  -webkit-animation: upDownMover 3s ease-in-out infinite;
          animation: upDownMover 3s ease-in-out infinite;
}

.pepecoin_about_content .pepecoin_heading_block {
  top: 30%;
  left: 50%;
  width: 670px;
  position: absolute;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.pepecoin_about_content .shape_dragonfly {
  top: 22px;
  left: 30px;
  max-width: 152px;
  position: absolute;
}
.pepecoin_about_content .shape_wood_1 {
  right: -120px;
  bottom: -30px;
  max-width: 330px;
  position: absolute;
}
.pepecoin_about_content .shape_wood_2 {
  left: -280px;
  bottom: -34px;
  max-width: 440px;
  position: absolute;
}

/* 4.03 - About Section - End
================================================== */
/* 4.04 - Table Section - Start
================================================== */
.ico_problem_solution_table {
  position: relative;
}
.ico_problem_solution_table:before {
  content: "";
  z-index: -2;
  opacity: 0.24;
  position: absolute;
  -webkit-filter: blur(22px);
          filter: blur(22px);
  inset: 20px 0 -8px 0;
  background: linear-gradient(145deg, #FC6500, #7064E9, #FC6500);
}
.ico_problem_solution_table .column_wrapper {
  z-index: 1;
  overflow: hidden;
  position: relative;
  border-radius: 20px;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.ico_problem_solution_table .column_wrapper:before {
  left: 50%;
  top: -200px;
  z-index: -1;
  content: "";
  opacity: 0.4;
  width: 400px;
  height: 400px;
  position: absolute;
  -webkit-filter: blur(30px);
          filter: blur(30px);
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background-image: radial-gradient(rgba(var(--bs-border-color-translucent), 0.7), var(--bs-light));
}
.ico_problem_solution_table .column_wrapper:after {
  left: 0;
  right: 0;
  bottom: -40px;
  content: "";
  z-index: -1;
  height: 40px;
  -webkit-filter: blur(45px);
          filter: blur(45px);
  position: absolute;
  background-image: linear-gradient(45deg, #FC6500, #7064E9);
}
.ico_problem_solution_table .column_wrapper > * {
  padding: 50px;
}
.ico_problem_solution_table .column_wrapper > *:not(:last-child) {
  border-style: solid;
  border-width: 0 1px 0 0;
  border-color: var(--bs-border-color);
}
.ico_problem_solution_table .heading_text {
  gap: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  line-height: 1;
  font-size: 32px;
  margin-bottom: 43px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.ico_problem_solution_table .heading_text .icon {
  width: 70px;
  height: 70px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  border-radius: 100%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}
.ico_problem_solution_table .heading_text .icon img {
  max-height: 36px;
}
@media screen and (min-width: 992px) {
  .ico_problem_solution_table .column_wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
  .ico_problem_solution_table .column_wrapper > * {
    width: 50%;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
  }
}
/* 4.04 - Table Section - End
================================================== */
/* 4.05 - Chart Section - Start
================================================== */
.ico_feature_section [class*=shape_shadow_] {
  width: 252px;
  -webkit-filter: blur(100px);
          filter: blur(100px);
}
.ico_feature_section .shape_shadow_1 {
  top: 80px;
  left: -120px;
}
.ico_feature_section .shape_shadow_2 {
  top: 30%;
  right: -140px;
}

.ico_investment_value {
  gap: 40px 30px;
  margin-bottom: 90px;
}
.ico_investment_value > li {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.ico_investment_value .heading_text {
  font-size: 20px;
  line-height: 28px;
  margin-bottom: 13px;
  letter-spacing: -0.4px;
}
.ico_investment_value .investment_value {
  gap: 4px;
  font-size: 18px;
  font-weight: 500;
  padding: 1px 18px;
  line-height: 28px;
  border-radius: 4px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}

.ico_coin_purchase_price {
  margin-bottom: 70px;
}
.ico_coin_purchase_price .purchase_price_rate {
  z-index: 1;
  font-size: 18px;
  font-weight: 500;
  position: relative;
}
.ico_coin_purchase_price .purchase_price_rate strong {
  font-weight: 700;
}
.ico_coin_purchase_price .chart_image {
  margin: -50px 0 -16px;
}
.ico_coin_purchase_price .live_values {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 16px;
  line-height: 28px;
  color: var(--bs-white);
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.ico_calculation_range .live_values {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 16px;
  line-height: 28px;
  color: var(--bs-white);
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.ico_calculation_range .range_image {
  margin: -16px 0;
}

/* 4.05 - Chart Section - End
================================================== */
/* 4.06 - Service Section - Start
================================================== */
.ico_service_section [class*=shape_shadow_] {
  width: 252px;
  -webkit-filter: blur(100px);
          filter: blur(100px);
}
.ico_service_section .shape_shadow_1 {
  left: -120px;
  bottom: -150px;
}
.ico_service_section .shape_shadow_2 {
  bottom: 20%;
  right: -140px;
}

.memecoin_service_section {
  padding: 180px 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

.memecoin_partners_logo {
  padding: 80px 0;
  margin: 70px 0 0;
  border-radius: 20px;
  background-size: cover;
  background-repeat: no-repeat;
  background-color: var(--bs-dark);
  background-position: center bottom -4px;
  border: 1px solid var(--bs-border-color);
}
.memecoin_partners_logo .memecoin_heading_block .highlight_title {
  line-height: 1;
  font-size: 16px;
}
.memecoin_partners_logo .ico_partner_logo {
  background-color: transparent;
}
.memecoin_partners_logo .ico_partner_logo:before {
  opacity: 0.3;
  background: var(--bs-primary);
}
.memecoin_partners_logo .ico_partner_logo:after {
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  height: auto;
  -webkit-transform: unset;
          transform: unset;
  border-radius: 10px;
}
.memecoin_partners_logo .ico_partner_logo .logo_wrap {
  height: 70px;
  padding: 0 20px;
  background-color: var(--bs-dark);
}
.memecoin_partners_logo .ico_partner_logo .logo_wrap img {
  opacity: 1;
  max-width: 150px;
}
.memecoin_partners_logo .ico_partner_logo:hover:before {
  opacity: 1;
}
.memecoin_partners_logo .ico_partner_logo:hover .logo_wrap img {
  opacity: 1;
}

.token_copy_board {
  gap: 10px;
  padding: 20px;
  font-size: 16px;
  margin-top: 60px;
  font-weight: 500;
  border-radius: 10px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  white-space: nowrap;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-white);
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}
.token_copy_board .icon {
  width: 32px;
  height: 32px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  border-radius: 4px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-dark);
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-light);
}
.token_copy_board .code mark {
  color: #4D5955;
}
.token_copy_board .copy_btn {
  min-width: 96px;
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  border-radius: 4px;
  color: var(--bs-dark);
  padding: 2px 10px 4px;
  background-color: var(--bs-secondary);
}
.token_copy_board .copy_btn:hover {
  background-color: var(--bs-light);
}

/* 4.06 - Service Section - End
================================================== */
/* 4.07 - Content Ticker Section - Start
================================================== */
.content_ticker_wrapper {
  z-index: 1;
  padding: 120px 0;
  overflow: hidden;
  position: relative;
  border-radius: 100%;
  border: 1px solid var(--bs-primary);
}
.content_ticker_wrapper:before {
  top: 50%;
  left: 50%;
  width: 34%;
  z-index: -1;
  content: "";
  height: 100%;
  position: absolute;
  border-radius: 100%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  border: 1px solid var(--bs-border-color);
  background-image: -webkit-gradient(linear, left bottom, left top, from(var(--bs-primary)), color-stop(transparent), color-stop(transparent), color-stop(transparent), to(var(--bs-primary)));
  background-image: linear-gradient(0deg, var(--bs-primary), transparent, transparent, transparent, var(--bs-primary));
}
.content_ticker_wrapper .shape_heart {
  top: 50%;
  left: 50%;
  z-index: 1;
  max-width: 303px;
  position: absolute;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.content_ticker_wrapper .shape_heart .coinpay_animate {
  top: 50%;
  left: 50%;
  max-width: 60%;
  margin-top: -10px;
  position: absolute;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.content_ticker_carousel {
  padding: 60px 0;
  border-style: solid;
  border-width: 1px 0;
  border-color: var(--bs-primary);
  background-color: var(--bs-dark);
}
.content_ticker_carousel .swiper-wrapper {
  -webkit-transition-timing-function: linear;
          transition-timing-function: linear;
}
.content_ticker_carousel .swiper-slide {
  line-height: 1;
  font-size: 170px;
  margin: -36px 0 -12px;
  color: var(--bs-primary);
  font-family: var(--bs-heading-font-family);
}

/* 4.07 - Content Ticker Section - End
================================================== */
/* 4.08 - Tokenomics Section - Start
================================================== */
.meme_tokenomics_section {
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: center top;
}
.meme_tokenomics_section .casino_image {
  position: relative;
}
.meme_tokenomics_section .casino_image .coinpay_animate {
  left: 50%;
  top: -14px;
  z-index: 1;
  max-width: 164px;
  position: absolute;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}

.meme_tokenomics_distribution {
  padding: 50px;
  border-radius: 20px;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-primary);
}
.meme_tokenomics_distribution .heading_text {
  line-height: 1;
  font-size: 22px;
  margin-bottom: 34px;
}
.meme_tokenomics_distribution ul {
  gap: 3px;
}
.meme_tokenomics_distribution ul > li {
  gap: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  line-height: 30px;
  -ms-flex-line-pack: center;
      align-content: center;
  color: var(--bs-white);
}
.meme_tokenomics_distribution ul > li:nth-child(1) .text {
  color: #DF5E08;
}
.meme_tokenomics_distribution ul > li:nth-child(2) .text {
  color: #FC1CCF;
}
.meme_tokenomics_distribution ul > li:nth-child(3) .text {
  color: #07C87F;
}
.meme_tokenomics_distribution ul > li:nth-child(4) .text {
  color: #49B7F7;
}
.meme_tokenomics_distribution ul > li:nth-child(5) .text {
  color: #DABA5E;
}
.meme_tokenomics_distribution ul > li:nth-child(6) .text {
  color: #FE9BE9;
}
.meme_tokenomics_distribution ul .icon {
  width: 16px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}
.meme_tokenomics_distribution ul .value {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  min-width: 56px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.meme_tokenomics_total_supply {
  line-height: 1;
  font-size: 20px;
  margin-bottom: 40px;
  color: var(--bs-white);
  font-family: var(--bs-heading-font-family);
}

.meme_tokenomics_benefits {
  overflow: hidden;
  position: relative;
  border-radius: 20px;
  padding: 30px 100px 0;
  background-image: -webkit-gradient(linear, left bottom, left top, from(var(--bs-primary)), to(transparent));
  background-image: linear-gradient(0deg, var(--bs-primary), transparent);
}
.meme_tokenomics_benefits:before {
  left: 50%;
  bottom: 0;
  z-index: 2;
  content: "";
  width: 310px;
  height: 120px;
  position: absolute;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background-image: -webkit-gradient(linear, left bottom, left top, from(var(--bs-primary)), to(transparent));
  background-image: linear-gradient(0deg, var(--bs-primary), transparent);
}

.benefits_features {
  position: relative;
  text-align: center;
}
.benefits_features .benefits_circle {
  gap: 4px;
  z-index: 1;
  width: 310px;
  height: 330px;
  margin: 0 auto;
  padding: 71px 0 0;
  position: relative;
  border-radius: 100%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin: 340px 0 -95px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  background-color: var(--bs-dark);
}
.benefits_features .benefits_circle:before, .benefits_features .benefits_circle:after {
  content: "";
  position: absolute;
  border-radius: inherit;
  border: 1px solid rgba(var(--bs-primary-rgb), 0.15);
  background-color: rgba(var(--bs-primary-rgb), 0.03);
}
.benefits_features .benefits_circle:before {
  inset: -54px;
}
.benefits_features .benefits_circle:after {
  inset: -126px;
}
.benefits_features .benefits_circle .percent {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  line-height: 1;
  font-size: 70px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: var(--bs-light);
  font-family: var(--bs-heading-font-family);
}
.benefits_features .benefits_circle .percent_title {
  line-height: 1;
  font-size: 20px;
  font-family: var(--bs-body-font-family);
}
.benefits_features [class*=benefits_feature_item_] {
  z-index: 1;
  width: 200px;
  position: absolute;
  border-radius: 10px;
  padding: 36px 30px 31px;
  background-image: -webkit-gradient(linear, left bottom, left top, from(var(--bs-primary)), color-stop(transparent), color-stop(transparent), to(var(--bs-primary)));
  background-image: linear-gradient(0deg, var(--bs-primary), transparent, transparent, var(--bs-primary));
}
.benefits_features [class*=benefits_feature_item_]:before {
  inset: 1px;
  z-index: -1;
  content: "";
  position: absolute;
  border-radius: inherit;
  background-color: var(--bs-dark);
}
.benefits_features [class*=benefits_feature_item_]:after {
  z-index: 2;
  content: "";
  width: 16px;
  height: 16px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  position: absolute;
  border-radius: 100%;
  outline: 8px solid #20242C;
  -webkit-box-shadow: 0 0 20px 10px var(--bs-primary);
          box-shadow: 0 0 20px 10px var(--bs-primary);
  background-image: -webkit-gradient(linear, left bottom, left top, from(var(--bs-primary)), to(var(--bs-dark)));
  background-image: linear-gradient(0deg, var(--bs-primary), var(--bs-dark));
}
.benefits_features [class*=benefits_feature_item_] .icon {
  font-size: 40px;
  margin: 0 0 20px;
  color: var(--bs-light);
}
.benefits_features [class*=benefits_feature_item_] .title {
  font-size: 16px;
  line-height: 24px;
}
.benefits_features .benefits_feature_item_1 {
  left: 110px;
  bottom: 376px;
}
.benefits_features .benefits_feature_item_1:after {
  right: -78px;
  bottom: -78px;
}
.benefits_features .benefits_feature_item_2 {
  left: 0;
  bottom: 120px;
}
.benefits_features .benefits_feature_item_2:after {
  right: -76px;
  bottom: -30px;
}
.benefits_features .benefits_feature_item_3 {
  right: 110px;
  bottom: 376px;
}
.benefits_features .benefits_feature_item_3:after {
  left: -78px;
  bottom: -78px;
}
.benefits_features .benefits_feature_item_4 {
  right: 0;
  bottom: 120px;
}
.benefits_features .benefits_feature_item_4:after {
  left: -76px;
  bottom: -30px;
}

.pepecoin_tokenomics_section .shape_shadow {
  left: 50%;
  width: 80%;
  bottom: -70px;
  -webkit-filter: blur(400px);
          filter: blur(400px);
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.pepecoin_tokenomics_section .shape_shadow img {
  width: 100%;
}
.pepecoin_tokenomics_section .shape_grash {
  left: 0;
  right: 0;
  bottom: 0;
}
.pepecoin_tokenomics_section .shape_grash img {
  width: 100%;
  display: block;
}

.pepecoin_token_supply {
  position: relative;
  padding: 70px 120px 130px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center center;
}
.pepecoin_token_supply .heading_text,
.pepecoin_token_supply .token_supply_value {
  line-height: 1;
  font-size: 60px;
  font-family: var(--bs-heading-font-family);
}
.pepecoin_token_supply p {
  font-size: 24px;
  font-weight: 600;
  margin-top: 10px;
  line-height: 34px;
}
.pepecoin_token_supply [class*=shape_tree_wood_] {
  bottom: -110px;
  max-width: 300px;
  position: absolute;
}
.pepecoin_token_supply .shape_tree_wood_1 {
  left: -30px;
}
.pepecoin_token_supply .shape_tree_wood_2 {
  left: 25%;
}
.pepecoin_token_supply .shape_tree_wood_3 {
  right: 24%;
}
.pepecoin_token_supply .shape_tree_wood_4 {
  right: -46px;
}

/* 4.08 - Tokenomics Section - End
================================================== */
/* 4.09 - Feature Section - Start
================================================== */
.pepecoin_feature_section .shape_tree img {
  max-width: 936px;
  margin-top: -140px;
  margin-right: -440px;
  display: inline-block;
}
.pepecoin_feature_section .shape_stone {
  z-index: 1;
  left: -50px;
  right: -50px;
  bottom: -70px;
}
.pepecoin_feature_section .shape_dragonfly {
  left: 40%;
  bottom: 140px;
  max-width: 150px;
}
.pepecoin_feature_section .shape_dragonfly img {
  -webkit-animation: upDownMover 3s ease-in-out infinite;
          animation: upDownMover 3s ease-in-out infinite;
}
.pepecoin_feature_section .shape_frog {
  left: 80px;
  bottom: 18px;
  max-width: 280px;
}

/* 4.09 - Feature Section - End
================================================== */
/* 4.10 - Roadmap Section - Start
================================================== */
.pepecoin_roadmap_section {
  background-image: radial-gradient(#3CA72F, var(--bs-primary));
}
.pepecoin_roadmap_section .shape_tree {
  bottom: 0;
  left: -800px;
  max-width: 1350px;
}
.pepecoin_roadmap_section .shape_stone {
  z-index: 1;
  left: -50px;
  right: -50px;
  bottom: -70px;
}
.pepecoin_roadmap_section .shape_cartoon_1 {
  left: 16%;
  z-index: 2;
  bottom: 16px;
  max-width: 320px;
}
.pepecoin_roadmap_section .shape_tree_wood {
  right: 0;
  bottom: 0;
  max-width: 480px;
}
.pepecoin_roadmap_section .pepe_coin_image {
  right: 128px;
  bottom: 170px;
  max-width: 260px;
}
.pepecoin_roadmap_section .pepe_coin_image img {
  -webkit-animation: coinFlip 4s infinite linear;
          animation: coinFlip 4s infinite linear;
}

.pepecoin_roadmap_list {
  top: 86px;
  left: 50%;
  gap: 124px;
  width: 100%;
  position: absolute;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.pepecoin_roadmap_list > li {
  line-height: 1;
  font-size: 44px;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}

/* 4.10 - Roadmap Section - End
================================================== */
/* 4.11 - Testimonial Section - Start
================================================== */
.testimonial_block {
  padding: 40px;
  border: 1px solid var(--bs-border-color);
}
.testimonial_block .testimonial_admin_logo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.testimonial_block .admin_logo {
  width: 170px;
  height: 52px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  padding: 0 18px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-dark);
  clip-path: polygon(100% 0, 86% 100%, 0 100%, 14% 0);
}
.testimonial_block .admin_logo img {
  max-height: 44px;
}
.testimonial_block .icon_block {
  width: auto;
  height: auto;
  border: none;
  max-width: 60px;
  border-radius: 0;
  overflow: visible;
  background: transparent;
}
.testimonial_block .icon_block svg {
  width: 100%;
}
.testimonial_block .icon_block svg path {
  fill: var(--bs-primary);
}
.testimonial_block .testimonial_admin_comment {
  font-size: 26px;
  line-height: 40px;
  margin: 30px 0 29px;
  padding-bottom: 39px;
  border-bottom: 1px solid var(--bs-border-color);
}

.testimonial_block.layout_2 {
  padding: 0;
  border: none;
  text-align: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.testimonial_block.layout_2 .testimonial_admin_comment {
  color: var(--bs-white);
  border-color: rgba(255, 255, 255, 0.1);
}
.testimonial_block.layout_2 .authorbox_block {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.testimonial_block.layout_2 .authorbox_block .authorbox_title {
  font-size: 28px;
  margin-bottom: 14px;
  color: var(--bs-white);
}
.testimonial_block.layout_2 .authorbox_block .authorbox_designation {
  line-height: 1;
  font-size: 18px;
  color: var(--bs-white);
}

.testimonial_carousel_2 {
  background-size: cover;
  background-repeat: no-repeat;
  background-blend-mode: overlay;
  background-position: center center;
  background-color: rgba(14, 14, 14, 0.7);
}
.testimonial_carousel_2 .swiper-wrapper {
  padding: 130px 0;
}
.testimonial_carousel_2 .testimonial_block.layout_2 {
  margin: auto;
  max-width: 856px;
}

.testimonial_block.layout_3 {
  border-width: 0;
  text-align: center;
  background-color: var(--bs-secondary);
}
.testimonial_block.layout_3 .authorbox_block {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.testimonial_block.layout_3 .testimonial_admin_comment {
  margin: 0;
  border-width: 0;
  color: var(--bs-heading-color);
}

.testimonial_carousel_3 .swiper-slide:not(.swiper-slide-active) {
  opacity: 0.3;
}

.testimonial_block.layout_4 {
  border-color: var(--bs-secondary);
  background-color: var(--bs-secondary);
}
.testimonial_block.layout_4 .quote_icon {
  max-width: 40px;
}
.testimonial_block.layout_4 .quote_icon svg path {
  fill: var(--bs-heading-color);
}
.testimonial_block.layout_4 .testimonial_admin_comment {
  font-size: 20px;
  line-height: 30px;
  margin: 24px 0 30px;
  padding-bottom: 26px;
}

/* 4.11 - Testimonial Section - End
================================================== */
/* 5.01 - Home Pages - Start
================================================== */
.index_ico {
  --bs-body-font-family: "DM Sans", serif;
  --bs-heading-font-family: "Roobert PRO Bold";
  --bs-body-bg: #070710;
  --bs-body-bg-rgb: 7, 7, 16;
  --bs-body-color: #D4D5F1;
  --bs-body-color-rgb: 212, 213, 241;
  --bs-heading-color: #CCCEEF;
  --bs-light: #0F1021;
  --bs-light-rgb: 15, 16, 33;
  --bs-primary: #FF3BD4;
  --bs-primary-rgb: 255, 59, 212;
  --bs-secondary: #7130C3;
  --bs-secondary-rgb: 113, 48, 195;
  --bs-border-color: #2F336D;
  --bs-border-color-translucent: 47, 51, 109;
  --bs-dark: #070710;
  --bs-dark-rgb: 7, 7, 16;
}
.index_ico .mobile_menu_btn:not(:hover) {
  border-color: rgba(255, 255, 255, 0.3);
}
.index_ico .dropdown-menu > li.active > .dropdown-item, .index_ico .dropdown-menu > li:hover > .dropdown-item {
  background-color: #0f1021;
}
.index_ico .iframe_block {
  background-color: var(--bs-secondary);
}
.index_ico .iframe_block iframe {
  mix-blend-mode: luminosity;
}
.index_ico .progress {
  background-color: rgba(255, 255, 255, 0.1);
}
.index_ico .progress .progress-bar {
  background: -webkit-gradient(linear, left top, right top, from(var(--bs-primary)), to(var(--bs-secondary)));
  background: linear-gradient(90deg, var(--bs-primary), var(--bs-secondary));
}
.index_ico .blog_section,
.index_ico .contact_section,
.index_ico .register_section,
.index_ico [class*=_details_section] {
  --bs-border-color: #21234B;
  --bs-border-color-translucent: 33, 35, 75;
}

.index_memecoin {
  --bs-body-font-family: "Bricolage Grotesque", serif;
  --bs-heading-font-family: "Dela Gothic One", serif;
  --bs-body-bg: #040612;
  --bs-body-bg-rgb: 4, 6, 18;
  --bs-body-color: #DCDEE9;
  --bs-body-color-rgb: 220, 222, 233;
  --bs-heading-color: #FFFFFF;
  --bs-light: #C5F57D;
  --bs-light-rgb: 197, 245, 125;
  --bs-primary: #0E9462;
  --bs-primary-rgb: 14, 148, 98;
  --bs-secondary: #FFCC00;
  --bs-secondary-rgb: 255, 204, 0;
  --bs-border-color: #0E9462;
  --bs-border-color-translucent: 14, 148, 98;
  --bs-dark: var(--bs-body-bg);
  --bs-dark-rgb: var(--bs-body-bg-rgb);
}
.index_memecoin .cb-cursor:before {
  background: var(--bs-secondary);
}
.index_memecoin h1, .index_memecoin h2, .index_memecoin h3, .index_memecoin h4, .index_memecoin h5, .index_memecoin h6 {
  font-weight: 400;
}
.index_memecoin mark {
  font-weight: 400;
  color: var(--bs-light);
}
.index_memecoin .line-1,
.index_memecoin .line-2,
.index_memecoin .line-3,
.index_memecoin .line-4 {
  background: var(--bs-primary);
}
.index_memecoin .countdown_timer_block {
  gap: 15px;
}
.index_memecoin .countdown_timer_block > li {
  gap: 4px;
  width: 100px;
  height: 80px;
  background-color: #070710;
}
.index_memecoin .countdown_timer_block > li:after {
  display: none;
}
.index_memecoin .countdown_timer_block span {
  font-size: 26px;
  font-weight: 400;
}
.index_memecoin .progress {
  background-color: rgba(255, 255, 255, 0.1);
}
.index_memecoin .progress .progress-bar {
  background: -webkit-gradient(linear, left top, right top, from(var(--bs-primary)), to(var(--bs-light)));
  background: linear-gradient(90deg, var(--bs-primary), var(--bs-light));
}
.index_memecoin .progress .progress-bar:after {
  color: var(--bs-dark);
}
.index_memecoin .social_block a {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 10px;
  color: var(--bs-white);
  background-color: var(--bs-primary);
}
.index_memecoin .social_block a:hover {
  color: var(--bs-dark);
  background-color: var(--bs-secondary);
}
.index_memecoin .pagelist_block a:hover {
  color: var(--bs-light);
}

.index_pepecoin {
  --bs-body-font-family: "Nunito Sans", serif;
  --bs-heading-font-family: "Lilita One", serif;
  --bs-body-bg: #FFF7AF;
  --bs-body-bg-rgb: 255, 247, 175;
  --bs-body-color: #113B1E;
  --bs-body-color-rgb: 17, 59, 30;
  --bs-heading-color: #113B1E;
  --bs-primary: #0B902B;
  --bs-primary-rgb: 11, 144, 43;
  --bs-secondary: #FBE354;
  --bs-secondary-rgb: 251, 227, 84;
  --bs-dark: #232222;
  --bs-dark-rgb: 35, 34, 34;
}
.index_pepecoin h1, .index_pepecoin h2, .index_pepecoin h3, .index_pepecoin h4, .index_pepecoin h5, .index_pepecoin h6 {
  font-weight: 400;
}
.index_pepecoin mark {
  font-weight: 400;
  color: var(--bs-secondary);
}
.index_pepecoin .backtotop .scroll {
  color: var(--bs-white);
  border: var(--bs-dark);
  background-color: var(--bs-dark);
}
.index_pepecoin .dropdown-menu {
  -webkit-box-shadow: 0 30px 50px rgba(0, 0, 0, 0.2);
          box-shadow: 0 30px 50px rgba(0, 0, 0, 0.2);
}

/* 5.01 - Home Pages - End
================================================== */
/* 5.02 - Details Pages - Start
================================================== */
[class*=_details_section] hr {
  margin: 48px 0;
}
[class*=_details_section] .details_image {
  overflow: hidden;
  border-radius: 20px;
  margin-bottom: 80px;
  background-color: var(--bs-secondary);
}
[class*=_details_section] .details_image img {
  mix-blend-mode: luminosity;
}
[class*=_details_section] .details_title {
  font-size: 48px;
  line-height: 58px;
  margin: 24px 0 22px;
}
[class*=_details_section] p {
  margin-bottom: 42px;
}
[class*=_details_section] .iconlist_block .iconlist_label {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  font-family: var(--bs-body-font-family);
}
[class*=_details_section] .iconlist_block .iconlist_icon {
  width: auto;
  margin: 10px 0 0;
}

.post_audio {
  margin-bottom: 46px;
}

.post_audio .audio_play_btn {
  gap: 26px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 50px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 5px 30px 5px 5px;
  color: var(--bs-heading-color);
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
  font-family: var(--bs-heading-font-family);
}

.post_audio .audio_play_btn i {
  width: 40px;
  height: 40px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  padding-left: 4px;
  border-radius: 100%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-white);
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-secondary);
}

.details_info_title {
  font-size: 26px;
  line-height: 36px;
  margin-bottom: 30px;
}

.postabmin_block {
  margin: 70px 0;
}

.other_post_nav {
  gap: 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 80px 0;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.other_post_nav > *:not(.blog_page_link) {
  gap: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 30px 40px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 20px;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.other_post_nav > *:not(.blog_page_link) .blog_post_title {
  font-size: 20px;
  line-height: 26px;
  margin-bottom: 10px;
}
.other_post_nav > *:not(.blog_page_link) .blog_post_title a:hover {
  color: var(--bs-primary);
}
.other_post_nav > *:not(.blog_page_link) .arrow {
  z-index: 1;
  font-size: 30px;
  position: relative;
}
.other_post_nav > *:not(.blog_page_link) .arrow i:before {
  font-weight: 400;
}
.other_post_nav > *:not(.blog_page_link) .arrow:before {
  top: 50%;
  z-index: -1;
  content: "";
  width: 34px;
  height: 34px;
  position: absolute;
  border-radius: 100%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}
.other_post_nav > *:not(.blog_page_link) .arrow:hover {
  color: var(--bs-white);
}
.other_post_nav > *:not(.blog_page_link) .arrow:hover:before {
  border-color: var(--bs-primary);
  background-color: var(--bs-primary);
}
.other_post_nav > *:not(.blog_page_link) .post_admin {
  gap: 6px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.other_post_nav > *:not(.blog_page_link) .post_admin:hover {
  color: var(--bs-primary);
}
.other_post_nav > *:not(.blog_page_link) .post_admin i {
  margin-top: -2px;
  display: inline-block;
  color: var(--bs-primary);
}
.other_post_nav .post_nav_prev {
  text-align: right;
}
.other_post_nav .post_nav_prev .arrow:before {
  left: -11px;
}
.other_post_nav .post_nav_prev .post_admin {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.other_post_nav .post_nav_prev .post_admin i {
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1;
}
.other_post_nav .post_nav_next .arrow {
  -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
          order: 1;
}
.other_post_nav .post_nav_next .arrow:before {
  right: -11px;
}
.other_post_nav .blog_page_link {
  font-size: 36px;
  background: -webkit-linear-gradient(-45deg, var(--bs-primary), var(--bs-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.comment_area {
  margin-bottom: 80px;
}

.comments_list {
  gap: 60px;
}
.comments_list > li .comments_list {
  gap: 40px;
  padding: 30px 0 0 110px;
}

.comment_item {
  gap: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.comment_item .comment_author_thumbnail {
  width: 80px;
  height: 80px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  overflow: hidden;
  border-radius: 100%;
}
.comment_item .comment_author_content {
  position: relative;
}
.comment_item .comment_author_name {
  line-height: 1;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 12px;
  font-family: var(--bs-heading-font-family);
}
.comment_item .comment_time {
  line-height: 1;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}
.comment_item .comment_reply_btn {
  top: 0;
  right: 0;
  font-size: 14px;
  font-weight: 500;
  position: absolute;
  border-radius: 50px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 4px 20px 5px;
  color: var(--bs-white);
  background: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.comment_item .comment_reply_btn:hover {
  color: var(--bs-white);
  border-color: var(--bs-secondary);
  background-color: var(--bs-secondary);
}

.comment_form {
  border-radius: 20px;
  margin-bottom: 80px;
  padding: 61px 70px 70px;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.comment_form .details_info_title {
  font-size: 26px;
  line-height: 36px;
  margin-bottom: 10px;
}
.comment_form p {
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 40px;
}
.comment_form .btn[type=submit] {
  width: 100%;
  display: block;
  padding: 21px 40px;
  border-radius: 6px;
}
.comment_form .form-group label {
  margin-bottom: 6px;
}
.comment_form .form-group .form-control {
  height: 60px;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}
.comment_form .form-group .form-control:focus {
  border-color: var(--bs-secondary);
}

.subscribe_box {
  position: relative;
  border-radius: 20px;
  padding: 51px 60px 60px;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.subscribe_box .bell_icon {
  top: 60px;
  right: 60px;
  width: 40px;
  height: 40px;
  font-size: 16px;
  position: absolute;
  border-radius: 100%;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-white);
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}
.subscribe_box .bell_icon i {
  -webkit-transform-origin: top;
          transform-origin: top;
  -webkit-animation: bellRing 1.8s ease-out infinite;
          animation: bellRing 1.8s ease-out infinite;
}
.subscribe_box .ico_newsletter_form input {
  border-radius: 50px;
  color: var(--bs-white);
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}
.subscribe_box .ico_newsletter_form input::-webkit-input-placeholder {
  color: #D4D5F1;
}
.subscribe_box .ico_newsletter_form input::-moz-placeholder {
  color: #D4D5F1;
}
.subscribe_box .ico_newsletter_form input:-ms-input-placeholder {
  color: #D4D5F1;
}
.subscribe_box .ico_newsletter_form input::-ms-input-placeholder {
  color: #D4D5F1;
}
.subscribe_box .ico_newsletter_form input::placeholder {
  color: #D4D5F1;
}
.subscribe_box .ico_newsletter_form input:-ms-input-placeholder {
  color: #D4D5F1;
}
.subscribe_box .ico_newsletter_form input::-ms-input-placeholder {
  color: #D4D5F1;
}
.subscribe_box .ico_newsletter_form .submit_btn {
  border-radius: 50px;
}
.subscribe_box .heading_text {
  font-size: 26px;
  line-height: 36px;
  margin-bottom: 6px;
}
.subscribe_box p {
  max-width: 388px;
}

.related_post_section [class*=heading_block] .heading_text {
  font-size: 40px;
  line-height: 50px;
}

/* 5.02 - Details Pages - End
================================================== */
/* 5.03 - Contact Page - Start
================================================== */
.contact_info_box {
  padding: 50px;
  border-radius: 20px;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.contact_info_box .social_block.style_4 a {
  width: 46px;
  height: 46px;
}
.contact_info_box .contact_info_list {
  margin-bottom: 40px;
}
.contact_info_box .iconlist_block.unordered_list_block {
  gap: 39px;
}

.contact_section .gmap_canvas {
  margin-top: 150px;
}

.gmap_canvas {
  z-index: 1;
  padding: 15px;
  position: relative;
  border-radius: 10px;
  background-image: linear-gradient(-45deg, #944FEB, #D85C37);
}
.gmap_canvas:before {
  inset: 1px;
  z-index: -1;
  content: "";
  position: absolute;
  border-radius: inherit;
  background-color: var(--bs-dark);
}
.gmap_canvas iframe {
  width: 100%;
  height: 700px;
  border-radius: inherit;
  mix-blend-mode: luminosity;
}

/* 5.03 - Contact Page - End
================================================== */
/* 5.04 - Register Pages - Start
================================================== */
.register_section {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-height: 100vh;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding: 130px 0 80px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-image: -webkit-gradient(linear, left bottom, left top, from(#070710), to(#591AA9));
  background-image: linear-gradient(0deg, #070710, #591AA9);
}
.register_section .shape_flower {
  top: 50%;
  left: 50%;
  width: 680px;
  position: absolute;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.register_section .shape_flower img {
  width: 100%;
}

.register_form {
  border-radius: 20px;
  padding: 40px 40px 50px;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.register_form .heading_text {
  font-size: 30px;
  line-height: 40px;
  margin-bottom: 15px;
}
.register_form p {
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 33px;
}
.register_form .btn_login_google {
  width: 100%;
}
.register_form .divider {
  margin: 30px 0;
}
.register_form .divider img {
  width: 100%;
  display: block;
}
.register_form .form-group:not(:last-child) {
  margin-bottom: 24px;
}
.register_form .input_title {
  margin-bottom: 6px;
}
.register_form .forget_pass a {
  color: var(--bs-secondary);
}
.register_form .form-check .form-check-label a {
  color: var(--bs-secondary);
}
.register_form button[type=submit] {
  width: 100%;
  margin-top: 30px;
  border-radius: 6px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

/* 5.04 - Register Pages - End
================================================== */

/* Token Swap Interface - Start
================================================== */
.token_swap_interface {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
}

.swap_container {
  width: 100%;
  max-width: 400px;
  background: rgba(42, 36, 109, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.swap_header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.swap_controls {
  display: flex;
  gap: 8px;
}

.control_btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.3s ease;
}

.control_btn:hover,
.control_btn.active {
  background: rgba(255, 255, 255, 0.2);
  color: var(--bs-white);
}

.swap_section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 8px;
}

.section_header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section_label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.network_selector {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  color: var(--bs-white);
  cursor: pointer;
  transition: all 0.3s ease;
}

.network_selector:hover {
  background: rgba(255, 255, 255, 0.15);
}

.network_icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.network_name {
  font-weight: 500;
}

.token_input_wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.token_selector {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.token_info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.token_info:hover {
  opacity: 0.8;
}

.token_icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
}

.token_logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.token_symbol {
  font-size: 18px;
  font-weight: 600;
  color: var(--bs-white);
}

.token_balance {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.balance_label {
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
}

.amount_input {
  flex: 1;
  text-align: right;
}

.swap_input {
  background: transparent;
  border: none;
  outline: none;
  color: var(--bs-white);
  font-size: 24px;
  font-weight: 600;
  text-align: right;
  width: 100%;
  max-width: 120px;
}

.swap_input::placeholder {
  color: rgba(255, 255, 255, 0.3);
}

.swap_arrow_container {
  display: flex;
  justify-content: center;
  margin: -4px 0;
  position: relative;
  z-index: 2;
}

.swap_arrow_btn {
  width: 32px;
  height: 32px;
  background: rgba(42, 36, 109, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.swap_arrow_btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--bs-white);
  transform: rotate(180deg);
}

.swap_details {
  padding: 12px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 12px;
}

.swap_rate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.fee_info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.fee_info i {
  font-size: 10px;
  opacity: 0.5;
}

.swap_action {
  margin-top: 16px;
}

.connect_wallet_btn {
  width: 100%;
  background: linear-gradient(135deg, #00D4FF, #00A8CC);
  border: none;
  border-radius: 12px;
  padding: 16px;
  color: var(--bs-white);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.connect_wallet_btn:hover {
  background: linear-gradient(135deg, #00A8CC, #007A99);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .swap_container {
    max-width: 350px;
    padding: 16px;
  }

  .token_symbol {
    font-size: 16px;
  }

  .swap_input {
    font-size: 20px;
    max-width: 100px;
  }
}
/* Token Swap Interface - End
================================================== */
/* 
Responsive For Mobile & Tablet Devices
================================================== 
* Project Name   :  Coinpay - Crypto Currency Site Template.
* File           :  CSS Base
* Version        :  1.0.0
* Author         :  XpressBuddy (https://themeforest.net/user/xpressbuddy/portfolio)
* Developer			 :	webrok (https://www.fiverr.com/webrok?up_rollout=true)
*	CSS code for responsive layout To make responsive
================================================== 
*/
/* Media Screen 1440px - Start
================================================== */
@media (max-width: 1440px) {
  .ico_site_footer {
    padding: 80px 0 0;
  }
  .ico_site_footer .shape_top {
    top: -90px;
  }
  .section_shadow_top:before {
    height: 60px;
  }
  .ico_hero_section .shape_globe {
    left: 40px;
    max-width: 100px;
  }
  .ico_hero_section .shape_coin {
    right: 40px;
    max-width: 150px;
  }
  .memecoin_hero_section [class*=shape_cartoon_] {
    max-width: 150px;
  }
  .memecoin_hero_section .shape_cartoon_1 {
    left: 6%;
  }
  .memecoin_hero_section .shape_cartoon_2 {
    right: 6%;
  }
  .memecoin_hero_section .shape_chain_2 {
    bottom: -90px;
  }
  .memecoin_hero_section .shape_chain_1 {
    top: -100px;
  }
  .pepecoin_site_footer .shape_tree {
    max-width: 870px;
  }
  .pepecoin_roadmap_section .shape_tree_wood {
    max-width: 350px;
  }
  .pepecoin_roadmap_section .pepe_coin_image {
    right: 96px;
    bottom: 120px;
    max-width: 180px;
  }
  .pepecoin_roadmap_section .shape_cartoon_1 {
    left: 18%;
    bottom: 0px;
    max-width: 220px;
  }
  .pepecoin_site_footer .shape_sign_board {
    bottom: 26px;
  }
  .pepecoin_roadmap_section .shape_tree {
    left: -750px;
    max-width: 1200px;
  }
}
/* Media Screen 1440px - End
================================================== */
/* Media Screen 1360px - Start
================================================== */
@media (max-width: 1360px) {
  .meme_site_footer .pagelist_block a {
    font-size: 18px;
  }
  .pagelist_block.unordered_list {
    gap: 16px 40px;
  }
  .pepecoin_about_content .shape_wood_2 {
    left: -110px;
    bottom: -14px;
    max-width: 260px;
  }
  .pepecoin_about_content .shape_wood_1 {
    right: -62px;
    bottom: -14px;
    max-width: 250px;
  }
  .pepecoin_about_section .shape_cloud {
    top: 12%;
    left: 8%;
    max-width: 190px;
  }
  .pepecoin_hero_section .shape_leaf_top {
    top: -40px;
  }
}
/* Media Screen 1360px - End
================================================== */
/* Media Screen 1199px - Start
================================================== */
@media (max-width: 1199px) {
  .whitepaper_info_wrap [class*=info_wrap_] {
    padding: 40px;
  }
  .ico_site_footer .middle_area > * {
    padding: 40px 30px;
  }
  .ico_site_footer .shape_top {
    top: -70px;
  }
  .section_shadow_top:before {
    height: 40px;
  }
  .pagelist_block.unordered_list {
    gap: 14px 40px;
  }
  .ico_iconbox_block .iconbox_description {
    max-width: 100%;
  }
  .ico_about_image .coin_image img {
    max-width: 400px;
  }
  .memecoin_hero_section .hero_title {
    font-size: 66px;
    line-height: 76px;
    margin-bottom: 18px;
  }
  .memecoin_hero_section .hero_description {
    margin: 0 auto 50px;
  }
  .memecoin_hero_section .hero_title .shape_image {
    max-width: 60px;
  }
  .memecoin_heading_block .heading_text {
    font-size: 48px;
    line-height: 58px;
  }
  .meme_progress_block .block_title {
    line-height: 24px;
  }
  .benefits_features .benefits_feature_item_1:after {
    right: -40px;
    bottom: -65px;
  }
  .benefits_features .benefits_feature_item_3:after {
    left: -40px;
    bottom: -65px;
  }
  .benefits_features .benefits_feature_item_2:after {
    right: -90px;
  }
  .benefits_features .benefits_feature_item_4:after {
    left: -90px;
  }
  .meme_accordion .accordion-item {
    padding: 40px;
  }
  .meme_accordion .accordion-button {
    gap: 20px;
  }
  .meme_site_footer .cartoon_shape_1 img {
    max-width: 100%;
  }
  .meme_site_footer .cartoon_shape_3 {
    top: 65%;
    max-width: 210px;
  }
  .meme_site_footer .cartoon_shape_2 {
    top: 62%;
  }
  .meme_site_footer .btns_group {
    margin-bottom: 300px;
  }
  .memecoin_hero_section [class*=shape_cartoon_] {
    max-width: 100px;
  }
  .memecoin_hero_section .shape_cartoon_1 {
    left: 2%;
  }
  .memecoin_hero_section .shape_cartoon_2 {
    right: 2%;
  }
  .memecoin_hero_section .shape_chain_2 {
    left: -40px;
    right: -40px;
    bottom: -44px;
  }
  .memecoin_hero_section .shape_chain_1 {
    top: -70px;
    left: -30px;
    right: -30px;
  }
  .memecoin_hero_section {
    padding: 190px 0 100px;
  }
  .content_ticker_carousel .swiper-slide {
    font-size: 130px;
    margin: -70px 0 -50px;
  }
  .content_ticker_wrapper .shape_heart {
    max-width: 160px;
  }
  .benefits_features [class*=benefits_feature_item_] {
    z-index: 1;
    top: unset;
    left: unset;
    right: unset;
    width: 200px;
    bottom: unset;
    position: relative;
  }
  .benefits_features [class*=benefits_feature_item_]:after {
    display: none;
  }
  .benefits_features {
    gap: 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .benefits_features .benefits_circle {
    padding: 0;
    margin: auto;
    width: 200px;
    height: 200px;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .benefits_features .benefits_circle .percent {
    font-size: 38px;
  }
  .benefits_features .benefits_circle .percent_title {
    font-size: 16px;
  }
  .benefits_features .benefits_circle:before {
    inset: -30px;
  }
  .benefits_features .benefits_circle:after {
    inset: -70px;
  }
  .meme_tokenomics_benefits:before {
    display: none;
  }
  .meme_tokenomics_benefits {
    padding: 30px 80px;
  }
  .pepecoin_hero_section .hero_title {
    font-size: 72px;
    margin: 0 0 18px 0;
  }
  .pepecoin_hero_section .hero_description {
    font-size: 22px;
    margin: 0 0 40px 0;
  }
  .pepecoin_hero_image .pepe_coin_image img {
    max-width: 350px;
  }
  .pepecoin_about_section .shape_tree {
    left: -520px;
    max-width: 1000px;
  }
  .pepecoin_heading_block .heading_text {
    font-size: 60px;
  }
  .pepecoin_feature_section .shape_frog {
    bottom: 0;
    left: 60px;
    max-width: 150px;
  }
  .pepecoin_feature_section .shape_tree img {
    max-width: 700px;
    margin-right: -340px;
  }
  .pepecoin_feature_section .shape_stone {
    left: -25px;
    right: -25px;
    bottom: -50px;
  }
  .pepecoin_token_supply p {
    font-size: 22px;
    line-height: 30px;
  }
  .pepecoin_token_supply [class*=shape_tree_wood_] {
    bottom: -88px;
    max-width: 250px;
  }
  .pepecoin_token_supply .shape_tree_wood_1 {
    left: -15px;
  }
  .pepecoin_token_supply .shape_tree_wood_2 {
    left: 26%;
  }
  .pepecoin_token_supply .shape_tree_wood_3 {
    right: 25%;
  }
  .pepecoin_token_supply .shape_tree_wood_4 {
    right: -30px;
  }
  .pepecoin_roadmap_list {
    top: 76px;
    gap: 110px;
  }
  .pepecoin_roadmap_list > li {
    font-size: 40px;
  }
  .pepecoin_roadmap_section .shape_stone {
    left: -25px;
    right: -25px;
    bottom: -50px;
  }
  .pepecoin_roadmap_section .shape_cartoon_1 {
    max-width: 184px;
  }
  .pepecoin_roadmap_section .shape_tree_wood {
    right: -38px;
    max-width: 260px;
  }
  .pepecoin_roadmap_section .pepe_coin_image {
    right: 34px;
    bottom: 92px;
    max-width: 130px;
  }
  .pepecoin_roadmap_section .shape_tree {
    left: -530px;
    max-width: 1000px;
  }
  .pepecoin_site_footer .shape_sign_board img {
    max-width: 1120px;
  }
  .pepecoin_site_footer {
    padding: 330px 0 0;
  }
  .pepecoin_site_footer .shape_cloud {
    top: 110px;
    right: 20px;
    max-width: 120px;
  }
  .pepecoin_site_footer .shape_dragonfly {
    top: 80px;
    left: 15px;
    max-width: 90px;
  }
  .page_header {
    padding: 170px 0 260px;
  }
  .meme_introducing_image .animate_line_image {
    top: -26px;
    right: 48px;
    max-width: 150px;
    display: inline-block;
  }
  .meme_process_heading .cartoon_image .animate_line_image {
    left: 80px;
    max-width: 234px;
  }
  .meme_tokenomics_section .casino_image .coinpay_animate {
    max-width: 150px;
  }
}
/* Media Screen 1199px - End
================================================== */
/* Media Screen 1024px - Start
================================================== */
@media (max-width: 1024px) {
  .ico_hero_section .shape_globe {
    left: 30px;
    max-width: 60px;
  }
  .ico_hero_section .shape_coin {
    right: 20px;
    max-width: 90px;
  }
  .ico_about_image .coin_image img {
    max-width: 350px;
  }
  .ico_coin_purchase_price .chart_image {
    margin: -20px 0 -16px;
  }
  .ico_team_block .team_avatar .avatar_wrap {
    width: 120px;
    height: 120px;
  }
  .ico_team_block .front_side_content,
  .ico_team_block .back_side_content {
    padding: 40px 20px;
  }
  .ico_team_block .team_member_name {
    font-size: 20px;
    margin-bottom: 8px;
  }
  .pepecoin_hero_section .shape_leaf_bottom {
    left: -90px;
    bottom: -40px;
  }
  .pepecoin_hero_section .hero_title {
    font-size: 60px;
    margin: 0 0 16px 0;
  }
  .pepecoin_hero_section .hero_description {
    font-size: 20px;
    margin: 0 0 34px 0;
  }
  .pepecoin_btn {
    font-size: 20px;
  }
  .header_pepecoin .main_menu_list > li > .nav-link {
    font-size: 16px;
  }
  .pepecoin_hero_section .shape_cloud {
    top: 140px;
    left: -60px;
    max-width: 140px;
  }
  .pepecoin_hero_image .pepe_coin_image img {
    max-width: 290px;
  }
  .pepecoin_heading_block .heading_text {
    font-size: 48px;
  }
  .pepecoin_heading_block .heading_text:has(.shape_dot) .shape_dot {
    max-width: 36px;
    margin: -6px 0 0;
  }
  .pepecoin_heading_block .heading_description {
    font-size: 20px;
    line-height: 30px;
  }
  .pepecoin_feature_block p {
    font-size: 16px;
    line-height: 24px;
  }
  .pepecoin_feature_block .feature_title {
    font-size: 26px;
  }
  .pepecoin_feature_block {
    padding: 90px 70px 70px;
  }
  .pepecoin_feature_block .feature_serial_number {
    width: 160px;
    height: 90px;
    font-size: 36px;
  }
  .pepecoin_about_content .pepecoin_heading_block {
    width: 570px;
  }
  .pepecoin_token_supply {
    padding: 70px 80px 130px;
  }
  .pepecoin_token_supply .heading_text,
  .pepecoin_token_supply .token_supply_value {
    font-size: 40px;
  }
  .pepecoin_token_supply p {
    font-size: 18px;
    line-height: 26px;
  }
  .pepecoin_token_supply [class*=shape_tree_wood_] {
    bottom: -70px;
    max-width: 200px;
  }
  .pepecoin_token_supply .shape_tree_wood_1 {
    left: -10px;
  }
  .pepecoin_token_supply .shape_tree_wood_2 {
    left: 27%;
  }
  .pepecoin_token_supply .shape_tree_wood_3 {
    right: 26%;
  }
  .pepecoin_token_supply .shape_tree_wood_4 {
    right: -20px;
  }
  .pepecoin_roadmap_list > li {
    font-size: 30px;
  }
  .pepecoin_roadmap_list {
    top: 70px;
    gap: 96px;
  }
  .pepecoin_roadmap_section .shape_cartoon_1 {
    left: 17%;
    max-width: 160px;
  }
  .social_block.style_3 a {
    width: 46px;
    height: 46px;
    font-size: 18px;
  }
  .social_block.style_3 a svg {
    width: 18px;
  }
  .pepecoin_site_footer .shape_tree {
    display: none;
  }
  .pepecoin_site_footer .shape_sign_board {
    display: none;
  }
  .pepecoin_site_footer {
    padding: 200px 0 0;
  }
  .pepecoin_site_footer .pepecoin_heading_block {
    margin-bottom: 50px;
  }
  .pepecoin_site_footer .shape_ground {
    margin-top: -40px;
  }
  [class*=_details_section] .details_title {
    font-size: 36px;
    line-height: 46px;
    margin: 22px 0 18px;
  }
  .comment_form,
  .subscribe_box {
    padding: 41px 50px 50px;
  }
  .subscribe_box .bell_icon {
    top: 50px;
    right: 50px;
  }
  .content_ticker_wrapper .shape_heart .coinpay_animate {
    margin-top: -4px;
  }
  .meme_introducing_image .animate_line_image {
    top: -23px;
    right: 44px;
    max-width: 126px;
  }
  .meme_process_heading .cartoon_image .animate_line_image {
    left: 70px;
    max-width: 186px;
  }
  .meme_tokenomics_section .casino_image .coinpay_animate {
    max-width: 130px;
  }
}
/* Media Screen 1024px - End
================================================== */
/* Media Screen 991px - Start
================================================== */
@media (max-width: 991px) {
  .container {
    max-width: 730px;
  }
  .ico_hero_section .ico_countdown_progress_box .countdown_timer_block {
    padding: 30px 20px;
    margin: 10px 0 20px;
    border-radius: 10px;
    background-image: none !important;
    background-color: var(--bs-light);
    border: 1px solid var(--bs-border-color);
  }
  .ico_countdown_progress_box .countdown_timer_block {
    gap: 20px 78px;
  }
  .ico_hero_section {
    padding: 150px 0 0;
    margin-bottom: 40px;
  }
  .ico_hero_section .hero_title {
    font-size: 54px;
    line-height: 64px;
  }
  .ico_hero_section .ico_countdown_progress_box {
    margin: 60px auto 16px;
  }
  .ico_partner_logo .logo_wrap {
    height: 90px;
    padding: 0 30px;
  }
  .ico_about_image {
    margin-top: 60px;
  }
  .ico_about_image .coin_image img {
    max-width: 100%;
  }
  .ico_problem_solution_table .column_wrapper > *:not(:last-child) {
    border-width: 0 0 1px 0;
  }
  .icon_coins_image {
    margin: 40px auto auto;
  }
  .whitepaper_image_wrap {
    height: auto;
    padding: 80px 30px;
    border-width: 0 0 1px 0;
  }
  .whitepaper_info_wrap [class*=info_wrap_] {
    padding: 30px;
  }
  .ico_site_footer .middle_area > *:first-child,
  .ico_site_footer .middle_area > *:last-child {
    width: 100%;
  }
  .ico_site_footer .middle_area > *:not(:first-child, :last-child) {
    width: 100%;
    border-width: 1px 0;
  }
  .ico_site_footer .middle_area {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    border-width: 1px;
    border-radius: 10px;
  }
  .ico_site_footer .middle_area > * {
    padding: 60px 30px;
  }
  .row:has(.memecoin_iconbox_block) > *:not(:last-child) {
    border-style: dashed;
    border-color: #C0DBA6;
    border-width: 0 0 1px 0;
  }
  .row:has(.memecoin_iconbox_block) {
    border-width: 1px;
  }
  .row:has(.memecoin_iconbox_block) .memecoin_iconbox_block {
    padding: 40px 20px;
  }
  .token_copy_board {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    margin: 30px 15px 0;
  }
  .meme_popup_video_area .image_block {
    float: none;
    max-width: 100%;
    margin: 50px auto auto;
  }
  .meme_site_footer .cartoon_shape_1 img {
    max-width: 350px;
  }
  .meme_site_footer {
    margin-top: -274px;
    padding: 400px 0 120px;
  }
  .meme_site_footer .footer_bottom {
    text-align: center;
  }
  .meme_site_footer .footer_bottom .social_block {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .meme_site_footer .footer_heading {
    font-size: 42px;
    margin-bottom: 50px;
  }
  .meme_site_footer .pagelist_block a {
    font-size: 16px;
  }
  .pagelist_block.unordered_list {
    gap: 14px 30px;
  }
  .meme_site_footer .btns_group {
    margin-bottom: 120px;
  }
  .meme_site_footer .cartoon_shape_3 {
    max-width: 150px;
  }
  .meme_tokenomics_section .shape_arrow_right {
    max-width: 250px;
    margin: 40px auto;
  }
  .meme_tokenomics_section .casino_image {
    margin: auto;
    max-width: 500px;
  }
  .content_ticker_carousel .swiper-slide {
    margin: -55px 0;
    font-size: 96px;
  }
  .content_ticker_wrapper .shape_heart {
    max-width: 148px;
  }
  .content_ticker_wrapper {
    padding: 70px 0;
  }
  .meme_process_section .cartoon_image {
    max-width: 300px;
    margin: auto 0 auto auto;
  }
  .pepecoin_hero_image .pepe_coin_image img {
    max-width: 430px;
  }
  .pepecoin_hero_section {
    text-align: center;
  }
  .pepecoin_hero_section .btns_group {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .pepecoin_hero_section .shape_tree,
  .pepecoin_about_section .shape_tree {
    display: none;
  }
  .pepecoin_about_section .shape_cloud {
    max-width: 120px;
  }
  .pepecoin_about_content .shape_dragonfly {
    max-width: 106px;
  }
  .pepecoin_about_content .shape_wood_2 {
    left: -90px;
    bottom: -12px;
    max-width: 190px;
  }
  .pepecoin_about_content .shape_wood_1 {
    max-width: 210px;
  }
  .pepecoin_feature_block {
    padding: 90px 110px 70px;
  }
  .pepecoin_token_supply .image_block {
    max-width: 220px;
  }
  .pepecoin_token_supply [class*=shape_tree_wood_] {
    bottom: -44px;
    max-width: 140px;
  }
  .pepecoin_roadmap_image {
    max-width: 500px;
    margin: auto auto 0;
  }
  .pepecoin_roadmap_list > li {
    font-size: 24px;
  }
  .pepecoin_roadmap_list {
    top: 62px;
    gap: 90px;
  }
  .pepecoin_roadmap_section .shape_tree_wood {
    bottom: -15px;
  }
  .pepecoin_roadmap_section .pepe_coin_image {
    right: 36px;
    bottom: 78px;
  }
  .pepecoin_site_footer .pepecoin_heading_block {
    text-align: center;
  }
  .pepecoin_site_footer .social_block.style_3 {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .pepecoin_feature_section .shape_stone {
    bottom: -20px;
  }
  .pepecoin_roadmap_section .shape_stone {
    bottom: -30px;
  }
  .pepecoin_roadmap_section .shape_cartoon_1 {
    max-width: 120px;
  }
  .pepecoin_roadmap_section .shape_tree_wood {
    bottom: -5px;
    max-width: 190px;
  }
  .pepecoin_roadmap_section .pepe_coin_image {
    right: 20px;
    bottom: 64px;
    max-width: 90px;
  }
  .pepecoin_site_footer {
    padding: 130px 0 0;
  }
  .pepecoin_site_footer .shape_ground {
    margin-top: 0;
  }
  .blog_carousel_block .blog_post_title {
    font-size: 36px;
    line-height: 42px;
    margin: 30px 0 17px;
  }
  .blog_post_left_image {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .blog_post_left_image .post_image {
    width: 100%;
  }
  .contact_section .gmap_canvas {
    margin-top: 80px;
  }
  .gmap_canvas iframe {
    height: 500px;
  }
  .ico_roadmap_flexbox {
    display: block;
  }
  .ico_roadmap_flexbox .roadmap_block:not(:last-child) {
    margin-bottom: 30px;
  }
  .roadmap_block .iconlist_block {
    opacity: 1;
    min-height: auto;
  }
  .roadmap_block .hover_shape {
    display: none;
  }
  .meme_process_heading .cartoon_image .animate_line_image {
    left: 80px;
    max-width: 210px;
  }
  .meme_tokenomics_section .casino_image .coinpay_animate {
    max-width: 158px;
  }
}
/* Media Screen 991px - End
================================================== */
/* Media Screen 767px - Start
================================================== */
@media (max-width: 767px) {
  .ico_problem_solution_table .column_wrapper > * {
    padding: 40px 30px;
  }
  .ico_problem_solution_table .heading_text {
    gap: 15px;
    font-size: 26px;
    margin-bottom: 30px;
  }
  .ico_problem_solution_table .heading_text .icon img {
    max-height: 30px;
  }
  .ico_problem_solution_table .heading_text .icon {
    width: 60px;
    height: 60px;
  }
  .memecoin_hero_section .hero_title {
    font-size: 60px;
    line-height: 66px;
  }
  .memecoin_hero_section .hero_title .shape_image {
    max-width: 54px;
  }
  .memecoin_hero_section .shape_cartoon_1 {
    left: -50px;
  }
  .memecoin_hero_section .shape_cartoon_2 {
    right: -50px;
  }
  .meme_process_heading {
    padding: 40px;
  }
  .meme_tokenomics_benefits {
    padding: 40px;
  }
  .benefits_features .benefits_circle {
    width: 160px;
    height: 160px;
  }
  .benefits_features .benefits_circle .percent {
    font-size: 30px;
  }
  .benefits_features .benefits_circle .percent_title {
    font-size: 14px;
  }
  .meme_site_footer .cartoon_shape_2 {
    left: -46px;
    max-width: 100px;
  }
  .meme_site_footer .cartoon_shape_3 {
    right: -50px;
    max-width: 140px;
  }
  .meme_progress {
    width: 100%;
  }
  .pepecoin_roadmap_section .shape_tree {
    left: -470px;
    max-width: 900px;
  }
  .iconlist_block.unordered_list_block {
    gap: 10px;
  }
  .other_post_nav {
    gap: 30px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .pepecoin_feature_section .shape_tree img {
    margin-top: -70px;
  }
}
/* Media Screen 767px - End
================================================== */
/* Media Screen 575px - Start
================================================== */
@media (max-width: 575px) {
  .ico_hero_section .hero_title {
    font-size: 48px;
    line-height: 54px;
  }
  .progress_range_step {
    -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
  }
  .progress_range_step > li {
    font-size: 14px;
    padding: 0 10px;
  }
  .ico_hero_section {
    margin-bottom: 80px;
  }
  .ico_heading_block .heading_text {
    font-size: 38px;
    line-height: 48px;
  }
  .rc-nav-button-prev {
    left: 0;
  }
  .rc-nav-button-next {
    right: 0;
  }
  [class*=rc-nav-button-] {
    z-index: 1;
  }
  .section_decoration .decoration_item:has(img[src*=shape_section_divider_]) {
    top: 40px;
  }
  .whitepaper_info_wrap .iconlist_block {
    gap: 14px;
    margin-bottom: 0;
  }
  .whitepaper_info_wrap .ico_creative_btn {
    margin-top: 40px;
  }
  .event_card_block .event_info {
    margin: 0;
    width: 100%;
    padding: 30px;
    border-radius: 0;
    border-width: 1px 0 0;
  }
  .event_card_block .event_date {
    width: 100%;
    height: auto;
    border-width: 0;
    position: sticky;
    padding: 20px 30px;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
  .event_block_left_image {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .event_block_left_image .event_image {
    max-width: 100%;
  }
  .section_shadow_top:before {
    height: 5px;
  }
  .ico_site_footer .shape_top {
    top: -40px;
  }
  .ico_accordion .icon_arrow {
    width: 70px;
  }
  .ico_accordion .accordion-item {
    padding: 20px 90px 20px 30px;
  }
  .ico_accordion .accordion-button {
    line-height: 26px;
  }
  .ico_accordion .icon_arrow svg {
    width: 20px;
  }
  .ico_accordion .accordion-body {
    padding: 20px 0 8px 0;
  }
  .memecoin_hero_section .hero_title {
    font-size: 48px;
    line-height: 58px;
  }
  .memecoin_hero_section .decoration_item {
    display: none;
  }
  .memecoin_hero_section {
    padding: 160px 0 100px;
  }
  .memecoin_heading_block .heading_text {
    font-size: 36px;
    line-height: 46px;
  }
  .memecoin_heading_block .heading_description {
    font-size: 16px;
    line-height: 24px;
  }
  .memecoin_hero_section .hero_description {
    font-size: 18px;
    line-height: 26px;
    margin: 0 auto 50px;
  }
  .memecoin_countdown_wraper {
    width: 100%;
    height: auto;
    margin-top: 30px;
    padding: 60px 30px;
    border-radius: 20px;
  }
  .memecoin_countdown_wraper .shape_circle,
  .memecoin_countdown_wraper .shape_cartoon,
  .memecoin_countdown_wraper .shape_cartoon_hand {
    display: none;
  }
  .token_copy_board {
    display: block;
  }
  .content_ticker_carousel .swiper-slide {
    font-size: 76px;
    margin: -44px 0;
  }
  .content_ticker_wrapper {
    padding: 50px 0;
  }
  .meme_eliments_section {
    padding: 0;
  }
  .pepecoin_hero_section .shape_cloud {
    top: 110px;
    left: -50px;
    max-width: 90px;
  }
  .pepecoin_hero_section .shape_leaf_top {
    top: -10px;
  }
  .pepecoin_hero_image .pepe_coin_image img {
    max-width: 340px;
  }
  .pepecoin_about_content .pepecoin_heading_block {
    width: 100%;
    position: sticky;
    -webkit-transform: unset;
            transform: unset;
  }
  .pepecoin_about_content .shape_board_image,
  .pepecoin_about_content .shape_dragonfly,
  .pepecoin_about_content .shape_wood_1,
  .pepecoin_about_content .shape_wood_2 {
    display: none;
  }
  .pepecoin_feature_section .shape_tree img {
    max-width: 500px;
    margin-right: -260px;
  }
  .pepecoin_feature_section .shape_dragonfly {
    bottom: 70px;
    max-width: 60px;
  }
  .pepecoin_feature_section .shape_frog {
    left: 30px;
    max-width: 90px;
  }
  .pepecoin_token_supply [class*=shape_tree_wood_] {
    bottom: -30px;
    max-width: 104px;
  }
  .pepecoin_roadmap_section .shape_stone {
    bottom: -16px;
  }
  .pepecoin_roadmap_section .shape_tree {
    left: -390px;
    max-width: 760px;
  }
  .pepecoin_hero_image .pepe_coin_image img {
    max-width: 235px;
  }
  .blog_carousel_block .blog_post_title {
    font-size: 30px;
    line-height: 36px;
    margin: 20px 0 12px;
  }
  .blog_carousel_block .swiper-slide {
    min-height: 500px;
  }
  .blog_carousel_block .badge {
    font-size: 14px;
  }
  .blog_carousel_block .bc-pagination {
    right: 30px;
    bottom: 30px;
  }
  .blog_post_left_image .post_info {
    padding: 30px;
    max-width: 100%;
  }
  .page_header {
    padding: 150px 0 220px;
  }
  [class*=_details_section] .details_title {
    font-size: 30px;
    line-height: 36px;
  }
  .details_info_title {
    font-size: 24px;
    line-height: 34px;
  }
  iframe {
    min-height: 300px;
  }
  .postabmin_block {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .comment_item {
    gap: 30px;
  }
  .comments_list > li .comments_list {
    gap: 30px;
    padding: 30px 0 0 60px;
  }
  .comment_form, .subscribe_box {
    padding: 40px;
  }
  .subscribe_box .bell_icon {
    top: 30px;
    right: 30px;
  }
  .ico_newsletter_form .submit_btn {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-top: 10px;
    position: sticky;
    text-align: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .page_title {
    font-size: 42px;
    line-height: 52px;
    margin-bottom: 18px;
  }
  .contact_info_box {
    padding: 40px;
  }
  .pepecoin_feature_section .shape_tree img {
    margin-top: 0;
  }
}
/* Media Screen 575px - End
================================================== */
/* Media Screen 425px - Start
================================================== */
@media (max-width: 425px) {
  .site_header .ico_btn_outline {
    padding: 14px 20px;
  }
  .site_header .btns_group {
    margin: 0 0 0 -40px;
  }
  .ico_hero_section .shape_globe {
    left: -30px;
  }
  .ico_hero_section .shape_coin {
    right: -44px;
  }
  .ico_countdown_progress_box .countdown_timer_block {
    gap: 15px;
  }
  .ico_countdown_progress_box .countdown_timer_block > li:after {
    display: none;
  }
  .progress_value {
    gap: 6px;
    margin-top: 16px;
  }
  .ico_hero_section .hero_title {
    font-size: 42px;
    line-height: 48px;
  }
  .ico_heading_block .heading_text {
    font-size: 34px;
    line-height: 42px;
  }
  .ico_coin_purchase_price .chart_image {
    margin: 0;
  }
  .section_space {
    padding-top: 100px;
    padding-bottom: 100px;
  }
  .section_decoration .decoration_item:has(img[src*=shape_section_divider_]) {
    top: 0;
  }
  .ico_tokenomics_section,
  .ico_whitepaper_section {
    margin-bottom: 60px;
  }
  .whitepaper_image_wrap {
    padding: 50px 30px;
  }
  .ico_newsletter_form .submit_btn {
    width: 100%;
    position: static;
    margin-top: 10px;
  }
  .memecoin_hero_section .hero_title {
    font-size: 42px;
    line-height: 48px;
  }
  .memecoin_heading_block .heading_text {
    font-size: 30px;
    line-height: 36px;
  }
  .meme_video_popup .title_text {
    font-size: 20px;
  }
  .video_popup_block i {
    font-size: 26px;
    padding: 0 0 0 6px;
  }
  .video_popup_block {
    width: 66px;
    height: 66px;
  }
  .content_ticker_carousel {
    padding: 10px 0;
  }
  .content_ticker_carousel .swiper-slide {
    font-size: 50px;
    margin: -6px 0 0;
  }
  .content_ticker_wrapper .shape_heart {
    max-width: 80px;
  }
  .meme_eliments_section {
    padding: 0;
  }
  .meme_site_footer .footer_heading {
    font-size: 36px;
    margin-bottom: 40px;
  }
  .meme_site_footer .btns_group {
    margin-bottom: 70px;
  }
  .pepecoin_btn {
    font-size: 16px;
    padding: 14px 26px 13px;
  }
  .pepecoin_hero_section .shape_leaf_bottom {
    bottom: -25px;
  }
  .pepecoin_hero_section {
    padding: 150px 0 60px;
  }
  .pepecoin_heading_block .heading_text {
    font-size: 36px;
  }
  .pepecoin_heading_block .heading_text:has(.shape_dot) .shape_dot {
    max-width: 16px;
    margin: -4px 0 0;
  }
  .pepecoin_heading_block .heading_text:has(.shape_dot) {
    gap: 0 10px;
  }
  .pepecoin_heading_block .heading_description {
    font-size: 18px;
    line-height: 26px;
  }
  .pepecoin_about_section .shape_cloud {
    top: 14%;
    left: -50px;
    max-width: 100px;
  }
  .pepecoin_feature_block {
    padding: 90px 70px 70px;
  }
  .pepecoin_token_supply {
    padding: 60px 30px 120px;
  }
  .pepecoin_token_supply .heading_text,
  .pepecoin_token_supply .token_supply_value {
    font-size: 26px;
  }
  .pepecoin_token_supply .image_block {
    max-width: 160px;
  }
  .pepecoin_token_supply p {
    font-size: 16px;
    line-height: 24px;
  }
  .pepecoin_token_supply [class*=shape_tree_wood_] {
    bottom: -20px;
    max-width: 84px;
  }
  .pepecoin_roadmap_section .shape_tree {
    display: none;
  }
  .pepecoin_roadmap_list {
    top: 50px;
    gap: 67px;
  }
  .pepecoin_roadmap_section .shape_cartoon_1 {
    left: 15px;
    max-width: 70px;
  }
  .pepecoin_roadmap_section .shape_tree_wood {
    max-width: 130px;
  }
  .pepecoin_roadmap_section .pepe_coin_image {
    right: 4px;
    bottom: 44px;
    max-width: 60px;
  }
  .pepecoin_site_footer .shape_dragonfly {
    top: 130px;
    left: 15px;
    max-width: 50px;
  }
  .pepecoin_site_footer .shape_cloud {
    top: 90px;
    right: 15px;
    max-width: 80px;
  }
  .blog_carousel_block .swiper-slide {
    min-height: 570px;
  }
  [class*=_details_section] .details_image {
    margin-bottom: 60px;
  }
  .iframe_block {
    margin-bottom: 50px;
  }
  iframe {
    min-height: 220px;
  }
  .subscribe_box .bell_icon {
    top: 6px;
    right: 6px;
  }
  .contact_info_list a {
    font-size: 18px;
    line-height: 20px;
  }
  .meme_process_heading .cartoon_image .animate_line_image {
    left: 74px;
    max-width: 174px;
  }
  .meme_tokenomics_section .casino_image .coinpay_animate {
    max-width: 127px;
  }
}
/* Media Screen 425px - End
================================================== */
/* Media Screen 375px - Start
================================================== */
@media (max-width: 375px) {
  .ico_partner_logo .logo_wrap img {
    max-width: 94px;
  }
  .tab_block .nav .nav-link {
    padding: 6px 20px;
  }
  .event_card_block .event_title {
    font-size: 24px;
    line-height: 30px;
    margin-bottom: 30px;
  }
  .pepecoin_hero_section .hero_title {
    font-size: 42px;
  }
  .pepecoin_hero_section .hero_description {
    font-size: 18px;
    margin: 0 0 30px 0;
  }
  .pepecoin_hero_image .pepe_coin_image img {
    max-width: 190px;
  }
  .pepecoin_feature_section .shape_stone {
    bottom: -10px;
  }
  .pepecoin_feature_section .shape_frog {
    left: 15px;
    max-width: 70px;
  }
  .pepecoin_feature_section .shape_dragonfly {
    bottom: 60px;
    max-width: 50px;
  }
  .pepecoin_roadmap_list > li {
    font-size: 20px;
  }
  .pepecoin_roadmap_list {
    top: 45px;
    gap: 58px;
  }
  .pepecoin_site_footer .copyright_text {
    font-size: 14px;
  }
  .pepecoin_site_footer .pepecoin_heading_block {
    margin-bottom: 30px;
  }
  .pepecoin_site_footer {
    padding: 110px 0 0;
  }
  .btns_group {
    gap: 15px;
  }
  .meme_tokenomics_section .casino_image .coinpay_animate {
    max-width: 115px;
  }
}
/* Media Screen 375px - End
================================================== */
/* Media Screen 320px - Start
================================================== */
@media (max-width: 320px) {
  .site_header .ico_btn_outline {
    font-size: 14px;
    padding: 10px 16px;
  }
  .mobile_menu_btn {
    width: 34px;
    height: 34px;
    font-size: 18px;
  }
  .site_header .nav_wrapper {
    padding: 20px 0;
  }
  .site_header .main_menu {
    top: 74px;
  }
  .site_header.sticky .main_menu {
    top: 54px;
  }
  .ico_hero_section {
    padding: 120px 0 0;
    margin-bottom: 100px;
  }
  .ico_about_image .coin_image img {
    max-width: 210px;
  }
  .pepecoin_hero_image .pepe_coin_image img {
    max-width: 150px;
  }
  .pepecoin_feature_block {
    padding: 90px 60px 70px;
  }
  .pepecoin_roadmap_list > li {
    font-size: 18px;
  }
  .pepecoin_roadmap_list {
    top: 42px;
    gap: 46px;
  }
  .meme_process_heading .cartoon_image .animate_line_image {
    left: 62px;
    max-width: 136px;
  }
  .meme_tokenomics_section .casino_image .coinpay_animate {
    max-width: 100px;
  }
}
/* Media Screen 320px - End
================================================== */