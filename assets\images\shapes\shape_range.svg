<svg width="624" height="68" viewBox="0 0 624 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect y="32" width="624" height="5" rx="2.5" fill="url(#paint0_linear_932_1040)"/>
<g filter="url(#filter0_f_932_1040)">
<circle cx="342" cy="34" r="14" fill="#7130C3"/>
</g>
<circle cx="342" cy="34" r="10" fill="#7130C3"/>
<circle cx="342" cy="34" r="5" fill="#070710"/>
<defs>
<filter id="filter0_f_932_1040" x="308" y="0" width="68" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_932_1040"/>
</filter>
<linearGradient id="paint0_linear_932_1040" x1="-4.02613" y1="33.8066" x2="109.394" y2="283.508" gradientUnits="userSpaceOnUse">
<stop offset="0.1066" stop-color="#DD00AC"/>
<stop offset="0.5303" stop-color="#7130C3"/>
<stop offset="0.9634" stop-color="#410093"/>
<stop offset="1" stop-color="#FF00EE" stop-opacity="0.26"/>
<stop offset="1" stop-color="#FF3BD4" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
