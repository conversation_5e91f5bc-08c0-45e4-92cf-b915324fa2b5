/* 1.01 - Template Fonts - Start
================================================== */
@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Dela+Gothic+One&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:opsz,wght@12..96,200..800&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:opsz,wght@12..96,200..800&amp;family=Lilita+One&amp;display=swap");
@import url("https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&amp;display=swap");
@font-face {
  font-family: "Roobert PRO Bold";
  src: url("../fonts/RoobertPRO-Bold.html") format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Roobert PRO Medium";
  src: url("../fonts/RoobertPRO-Medium.html") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "January Night";
  src: url("../fonts/January-Night.html") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
/* 1.01 - Template Fonts - End
================================================== */
:root {
  --bs-body-font-family: "DM Sans", serif;
  --bs-heading-font-family: "DM Sans", serif;
  --bs-body-font-size: 16px;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 26px;
  --bs-transition: 400ms ease;
}

/* 1.02 - Template Reset - Start
================================================== */
body {
  margin: 0;
  padding: 0;
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  font-style: normal;
  font-optical-sizing: auto;
  color: var(--bs-body-color);
  font-family: var(--bs-body-font-family);
  background-color: var(--bs-body-bg);
  text-rendering: optimizelegibility;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

.page_wrapper {
  overflow: hidden;
  position: relative;
}

iframe {
  width: 100%;
  border: none;
  display: block;
  min-height: 440px;
}

.iframe_block {
  overflow: hidden;
  border-radius: 20px;
  margin-bottom: 70px;
}

a:focus,
a:active,
input,
input:hover,
input:focus,
input:active,
textarea,
textarea:hover,
textarea:focus,
textarea:active {
  outline: none;
}

img:not([draggable]),
embed,
object,
video {
  height: auto;
  max-width: 100%;
}

img {
  border: none;
  height: auto;
  max-width: 100%;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  vertical-align: middle;
}

a {
  outline: 0;
  display: inline-block;
  text-decoration: none;
  color: var(--bs-heading-color);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  font-family: var(--bs-body-font-family);
}
a:active, a:focus, a:hover, a:visited {
  outline: 0;
  text-decoration: none;
}

button {
  padding: 0;
  border: none;
  outline: none;
  background: none;
  display: inline-block;
  color: var(--bs-heading-color);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  font-family: var(--bs-body-font-family);
}
button:focus {
  outline: none;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}

b, strong {
  font-weight: 700;
}

p strong {
  font-size: 18px;
  font-weight: 500;
}

hr {
  opacity: 1;
  height: 1px;
  border: none;
  margin: 38px 0;
  background-color: var(--bs-border-color);
}

mark {
  padding: 0;
  font-weight: 700;
  color: var(--bs-primary);
  background-color: transparent;
}

.container {
  max-width: 1320px;
  padding-left: 15px;
  padding-right: 15px;
}

.container-fluid {
  padding-left: 30px;
  padding-right: 30px;
}

.row {
  margin: -15px;
}

[class*=col-] {
  padding: 15px;
}

.backtotop {
  right: 15px;
  z-index: 999;
  bottom: 60px;
  display: none;
  position: fixed;
}

.backtotop .scroll {
  z-index: 1;
  width: 42px;
  height: 42px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 16px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.backtotop .scroll:hover {
  color: var(--bs-dark);
  border-color: var(--bs-white);
  background-color: var(--bs-white);
}

#preloader {
  inset: 0;
  gap: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  z-index: 9999;
  position: fixed;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-dark);
}

.line-1,
.line-2,
.line-3,
.line-4 {
  width: 5px;
  height: 30px;
  background: var(--bs-secondary);
  -webkit-animation: scaleUpDown 1.2s infinite ease-in-out;
          animation: scaleUpDown 1.2s infinite ease-in-out;
}

.line-2 {
  -webkit-animation-delay: 0.2s;
          animation-delay: 0.2s;
}

.line-3 {
  -webkit-animation-delay: 0.4s;
          animation-delay: 0.4s;
}

.line-4 {
  -webkit-animation-delay: 0.6s;
          animation-delay: 0.6s;
}

@-webkit-keyframes scaleUpDown {
  0%, 100% {
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  50% {
    -webkit-transform: scaleY(2);
            transform: scaleY(2);
  }
}

@keyframes scaleUpDown {
  0%, 100% {
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
  }
  50% {
    -webkit-transform: scaleY(2);
            transform: scaleY(2);
  }
}
.dropdown-menu {
  border: none;
  padding: 6px 0;
  margin-top: 16px;
  border-radius: 6px;
  background-color: var(--bs-white);
  -webkit-box-shadow: 0 30px 50px rgba(0, 0, 0, 0.6);
          box-shadow: 0 30px 50px rgba(0, 0, 0, 0.6);
  -webkit-animation: 0.2s ease-in-out 0s normal none 1 running fadeIn;
          animation: 0.2s ease-in-out 0s normal none 1 running fadeIn;
}
.dropdown-menu:before {
  left: 0;
  right: 0;
  top: -16px;
  content: "";
  height: 16px;
  display: block;
  position: absolute;
}
.dropdown-menu > li {
  padding: 0 6px;
}
.dropdown-menu > li:not(:last-child) {
  margin-bottom: 1px;
}
.dropdown-menu > li > .dropdown-item {
  display: block;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  position: relative;
  border-radius: 4px;
  white-space: nowrap;
  color: var(--bs-dark);
  padding: 12px 18px 11px;
}
.dropdown-menu > li > .dropdown-item .nav_link_icon {
  color: inherit;
}
.dropdown-menu > li.active > .dropdown-item, .dropdown-menu > li:hover > .dropdown-item {
  color: var(--bs-white);
  background-color: var(--bs-primary);
}
.dropdown-menu > li.active > .dropdown-item .nav_link_icon, .dropdown-menu > li:hover > .dropdown-item .nav_link_icon {
  color: var(--bs-white);
}

.section_decoration {
  z-index: 1;
  position: relative;
}
.section_decoration .decoration_item {
  z-index: -1;
  position: absolute;
}
.section_decoration .decoration_item img[src*=shape_section_divider_] {
  width: 100%;
}
.section_decoration .decoration_item:has(img[src*=shape_section_divider_]) {
  top: 0;
  left: 0;
  right: 0;
}
.section_decoration .shape_net_top {
  left: 0;
  right: 0;
  top: -194px;
}
.section_decoration .shape_net_top img {
  width: 100%;
  height: auto;
}

/* 1.02 - Template Reset - End
================================================== */
/* 2.01 - CSS Animations - Start
================================================== */
@-webkit-keyframes upDownMover {
  0%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
  }
}
@keyframes upDownMover {
  0%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
  }
}
.upDownMover {
  -webkit-animation: upDownMover 4s ease-in-out infinite;
          animation: upDownMover 4s ease-in-out infinite;
}

@-webkit-keyframes fadeInOut {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
.fadeInOut {
  -webkit-animation: fadeInOut 4s ease-in-out infinite;
          animation: fadeInOut 4s ease-in-out infinite;
}

@-webkit-keyframes gradientChange {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes gradientChange {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
.gradientChange {
  -webkit-animation: gradientChange 8s ease infinite;
          animation: gradientChange 8s ease infinite;
}

.section_shadow_top {
  z-index: 1;
  position: relative;
}
.section_shadow_top:before {
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
  content: "";
  height: 125px;
  opacity: 0.4;
  position: absolute;
  -webkit-filter: blur(44px);
          filter: blur(44px);
  border-radius: 50px;
  background-size: 400% 400%;
  -webkit-animation: gradientChange 8s ease infinite;
          animation: gradientChange 8s ease infinite;
  background-image: linear-gradient(45deg, var(--bs-primary), var(--bs-secondary));
}

@-webkit-keyframes animationSpin {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@keyframes animationSpin {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
.animationSpin {
  -webkit-animation: spin 5000ms linear infinite;
          animation: spin 5000ms linear infinite;
}

@-webkit-keyframes ripple {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(1.75);
            transform: scale(1.75);
  }
}

@keyframes ripple {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    -webkit-transform: scale(1.75);
            transform: scale(1.75);
  }
}
@-webkit-keyframes coinFlip {
  0% {
    -webkit-transform: rotateY(-180deg);
            transform: rotateY(-180deg);
  }
  80% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg);
  }
  100% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg);
  }
}
@keyframes coinFlip {
  0% {
    -webkit-transform: rotateY(-180deg);
            transform: rotateY(-180deg);
  }
  80% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg);
  }
  100% {
    -webkit-transform: rotateY(180deg);
            transform: rotateY(180deg);
  }
}
@-webkit-keyframes bellRing {
  0% {
    -webkit-transform: rotate(0) scale(1) skew(1deg);
            transform: rotate(0) scale(1) skew(1deg);
  }
  10% {
    -webkit-transform: rotate(-15deg) scale(1) skew(1deg);
            transform: rotate(-15deg) scale(1) skew(1deg);
  }
  20% {
    -webkit-transform: rotate(30deg) scale(1) skew(1deg);
            transform: rotate(30deg) scale(1) skew(1deg);
  }
  30% {
    -webkit-transform: rotate(-15deg) scale(1) skew(1deg);
            transform: rotate(-15deg) scale(1) skew(1deg);
  }
  40% {
    -webkit-transform: rotate(30deg) scale(1) skew(1deg);
            transform: rotate(30deg) scale(1) skew(1deg);
  }
  100%, 50% {
    -webkit-transform: rotate(0) scale(1) skew(1deg);
            transform: rotate(0) scale(1) skew(1deg);
  }
}
@keyframes bellRing {
  0% {
    -webkit-transform: rotate(0) scale(1) skew(1deg);
            transform: rotate(0) scale(1) skew(1deg);
  }
  10% {
    -webkit-transform: rotate(-15deg) scale(1) skew(1deg);
            transform: rotate(-15deg) scale(1) skew(1deg);
  }
  20% {
    -webkit-transform: rotate(30deg) scale(1) skew(1deg);
            transform: rotate(30deg) scale(1) skew(1deg);
  }
  30% {
    -webkit-transform: rotate(-15deg) scale(1) skew(1deg);
            transform: rotate(-15deg) scale(1) skew(1deg);
  }
  40% {
    -webkit-transform: rotate(30deg) scale(1) skew(1deg);
            transform: rotate(30deg) scale(1) skew(1deg);
  }
  100%, 50% {
    -webkit-transform: rotate(0) scale(1) skew(1deg);
            transform: rotate(0) scale(1) skew(1deg);
  }
}
.bellRing {
  -webkit-transform-origin: top;
          transform-origin: top;
  -webkit-animation: ring 1.8s ease-out infinite;
          animation: ring 1.8s ease-out infinite;
}

@-webkit-keyframes smallZoomInOut {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.04);
            transform: scale(1.04);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}

@keyframes smallZoomInOut {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(1.04);
            transform: scale(1.04);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
.smallZoomInOut {
  -webkit-animation: smallZoomInOut 1s infinite ease-in-out;
          animation: smallZoomInOut 1s infinite ease-in-out;
}

/* 2.01 - CSS Animations - End
================================================== */
/* 2.02 - Spacing and Gaps - Start
================================================== */
.section_space {
  padding-top: 150px;
  padding-bottom: 150px;
}

/* 2.02 - Spacing and Gaps - End
================================================== */
/* 2.04 - Button - Start
================================================== */
.btns_group {
  gap: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 60px 0;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.btn {
  gap: 6px;
  line-height: 1;
  font-size: 18px;
  font-weight: 600;
  padding: 15px 30px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 50px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-white);
  border-color: var(--bs-secondary);
  background-color: var(--bs-secondary);
}
.btn:hover {
  color: var(--bs-white);
  border-color: var(--bs-primary);
  background-color: var(--bs-primary);
}

.btn_link {
  gap: 10px;
  line-height: 1;
  font-size: 18px;
  font-weight: 600;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-bottom: 2px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-bottom: 1px solid var(--bs-body-color);
}
.btn_link > * {
  color: var(--bs-body-color);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.btn_link:hover {
  border-color: var(--bs-primary);
}
.btn_link:hover > * {
  color: var(--bs-primary);
}
.btn_link:hover .btn_icon {
  -webkit-transform: translateX(2px);
          transform: translateX(2px);
}

.btn_login_google {
  gap: 10px;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  padding: 11px 30px;
  border-radius: 6px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}
.btn_login_google .icon {
  width: 20px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}
.btn_login_google .icon img {
  width: 100%;
}

@-webkit-keyframes btnMaskOffHover {
  from {
    -webkit-mask-position: 100% 0;
            mask-position: 100% 0;
  }
  to {
    -webkit-mask-position: 0 0;
            mask-position: 0 0;
  }
}

@keyframes btnMaskOffHover {
  from {
    -webkit-mask-position: 100% 0;
            mask-position: 100% 0;
  }
  to {
    -webkit-mask-position: 0 0;
            mask-position: 0 0;
  }
}
@-webkit-keyframes btnMaskOnHover {
  from {
    -webkit-mask-position: 0 0;
            mask-position: 0 0;
  }
  to {
    -webkit-mask-position: 100% 0;
            mask-position: 100% 0;
  }
}
@keyframes btnMaskOnHover {
  from {
    -webkit-mask-position: 0 0;
            mask-position: 0 0;
  }
  to {
    -webkit-mask-position: 100% 0;
            mask-position: 100% 0;
  }
}
.memecoin_btn {
  z-index: 1;
  font-size: 20px;
  overflow: hidden;
  font-weight: 600;
  line-height: 24px;
  text-align: center;
  position: relative;
  padding: 23px 50px;
  border-radius: 50px;
  font-family: var(--bs-body-font-family);
}
.memecoin_btn .btn_bg {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  position: absolute;
  -webkit-animation: btnMaskOffHover 0.6s steps(70) forwards;
          animation: btnMaskOffHover 0.6s steps(70) forwards;
}
.memecoin_btn:hover .btn_bg {
  -webkit-animation: btnMaskOnHover 0.6s steps(70) forwards;
          animation: btnMaskOnHover 0.6s steps(70) forwards;
}

.memecoin_btn.bg-white {
  color: var(--bs-dark);
}
.memecoin_btn.bg-white .btn_bg {
  background: var(--bs-secondary);
  -webkit-mask-size: 7100%, 100%;
          mask-size: 7100%, 100%;
}

.memecoin_btn.bg-light:hover {
  color: var(--bs-dark);
}
.memecoin_btn.bg-light .btn_bg {
  background: var(--bs-dark);
  -webkit-mask-size: 7100%, 100%;
          mask-size: 7100%, 100%;
}

.pepecoin_btn {
  z-index: 1;
  line-height: 1;
  font-size: 24px;
  font-weight: 400;
  position: relative;
  padding: 19px 38px 18px;
  text-transform: uppercase;
  font-family: var(--bs-heading-font-family);
}
.pepecoin_btn .btn_bg {
  inset: 0;
  z-index: -1;
  position: absolute;
}
.pepecoin_btn .btn_bg svg {
  width: 100%;
  height: 100%;
  display: block;
  -webkit-transition: 0.2s ease-in-out;
  transition: 0.2s ease-in-out;
}
.pepecoin_btn:hover {
  -webkit-transform: translate(-1px, -1px);
          transform: translate(-1px, -1px);
}
.pepecoin_btn:hover .btn_bg svg {
  -webkit-filter: drop-shadow(6px 6px 0px var(--bs-dark));
          filter: drop-shadow(6px 6px 0px var(--bs-dark));
}

.ico_btn_outline {
  gap: 8px;
  z-index: 1;
  line-height: 1;
  font-size: 20px;
  font-weight: 700;
  border-radius: 6px;
  padding: 20px 40px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
  background: -webkit-gradient(linear, left top, right top, from(var(--bs-primary)), to(var(--bs-secondary)));
  background: linear-gradient(90deg, var(--bs-primary), var(--bs-secondary));
}
.ico_btn_outline:before {
  inset: 1px;
  z-index: -1;
  content: "";
  position: absolute;
  border-radius: 6px;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  background: -webkit-gradient(linear, left top, right top, from(rgba(var(--bs-secondary-rgb), 0.96)), color-stop(24%, var(--bs-dark)));
  background: linear-gradient(90deg, rgba(var(--bs-secondary-rgb), 0.96) 0%, var(--bs-dark) 24%);
}
.ico_btn_outline:hover {
  color: var(--bs-white);
}
.ico_btn_outline:hover:before {
  opacity: 0;
}

.ico_creative_btn {
  z-index: 1;
  content: "";
  padding: 1px;
  font-size: 16px;
  font-weight: 700;
  overflow: hidden;
  border-radius: 6px;
  text-align: center;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-primary);
  -webkit-box-shadow: 0px -8px 18px -16px rgba(var(--bs-primary-rgb), 1);
          box-shadow: 0px -8px 18px -16px rgba(var(--bs-primary-rgb), 1);
  background: -webkit-gradient(linear, left top, right top, from(var(--bs-primary)), to(var(--bs-secondary)));
  background: linear-gradient(90deg, var(--bs-primary), var(--bs-secondary));
}
.ico_creative_btn .btn_wrapper {
  z-index: 1;
  padding: 10px;
  overflow: hidden;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-radius: inherit;
  background-color: var(--bs-dark);
}
.ico_creative_btn .btn_wrapper:before {
  left: 0;
  right: 0;
  top: 60%;
  height: 100%;
  content: "";
  z-index: -1;
  opacity: 0.3;
  position: absolute;
  -webkit-filter: blur(12px);
          filter: blur(12px);
  border-radius: 50%;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  background-image: radial-gradient(#FF3BD4, #7130C3, transparent);
}
.ico_creative_btn [class*=btn_icon_] {
  width: 66px;
  height: 40px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.ico_creative_btn [class*=dot_] {
  width: 5px;
  height: 6px;
  position: absolute;
  border-radius: 5px;
  display: inline-block;
  border: 1px solid #2A246D;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.ico_creative_btn .btn_icon_left .dot_top {
  top: 0;
  left: 0;
}
.ico_creative_btn .btn_icon_left .dot_bottom {
  left: 0;
  bottom: 0;
}
.ico_creative_btn .btn_icon_right .dot_top {
  top: 0;
  right: 0;
}
.ico_creative_btn .btn_icon_right .dot_bottom {
  right: 0;
  bottom: 0;
}
.ico_creative_btn [class*=icon_arrow_] {
  width: 25px;
  overflow: visible;
  fill: transparent;
}
.ico_creative_btn [class*=icon_arrow_] path {
  stroke: var(--bs-primary);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.ico_creative_btn .icon_arrow_left {
  -webkit-transform: translateX(4px);
          transform: translateX(4px);
}
.ico_creative_btn .icon_arrow_right {
  -webkit-transform: translateX(-4px);
          transform: translateX(-4px);
}
.ico_creative_btn:hover {
  color: var(--bs-white);
  -webkit-transform: translateY(-6px);
          transform: translateY(-6px);
  -webkit-box-shadow: 0px 24px 26px -20px rgba(var(--bs-primary-rgb), 0.6);
          box-shadow: 0px 24px 26px -20px rgba(var(--bs-primary-rgb), 0.6);
  background: -webkit-gradient(linear, left bottom, left top, from(var(--bs-primary)), to(var(--bs-secondary)));
  background: linear-gradient(0deg, var(--bs-primary), var(--bs-secondary));
}
.ico_creative_btn:hover .btn_wrapper:before {
  opacity: 1;
  -webkit-transform: scale(2);
          transform: scale(2);
}
.ico_creative_btn:hover [class*=dot_] {
  border-color: var(--bs-white);
}
.ico_creative_btn:hover [class*=icon_arrow_] path {
  stroke: var(--bs-white);
}
.ico_creative_btn:hover .icon_arrow_left path:nth-child(1) {
  -webkit-transform: translate(-6px, 4px);
          transform: translate(-6px, 4px);
}
.ico_creative_btn:hover .icon_arrow_left path:nth-child(2) {
  -webkit-transform: translateX(-6px);
          transform: translateX(-6px);
}
.ico_creative_btn:hover .icon_arrow_left path:nth-child(3) {
  -webkit-transform: translate(-6px, -4px);
          transform: translate(-6px, -4px);
}
.ico_creative_btn:hover .icon_arrow_right path:nth-child(1) {
  -webkit-transform: translate(6px, -4px);
          transform: translate(6px, -4px);
}
.ico_creative_btn:hover .icon_arrow_right path:nth-child(2) {
  -webkit-transform: translateX(6px);
          transform: translateX(6px);
}
.ico_creative_btn:hover .icon_arrow_right path:nth-child(3) {
  -webkit-transform: translate(6px, 4px);
          transform: translate(6px, 4px);
}

.ico_btn_link {
  gap: 8px;
  line-height: 1;
  font-size: 20px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding-bottom: 12px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  color: var(--bs-primary);
  font-family: var(--bs-heading-font-family);
}
.ico_btn_link:before {
  left: 0;
  bottom: 0;
  content: "";
  width: 100%;
  height: 2px;
  border-radius: 6px;
  position: absolute;
  background-image: -webkit-gradient(linear, left top, right top, from(#FF3BD4), to(#2A246D));
  background-image: linear-gradient(90deg, #FF3BD4, #2A246D);
}
.ico_btn_link .btn_icon {
  width: 25px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.ico_btn_link .icon_arrow {
  overflow: visible;
}
.ico_btn_link .icon_arrow path {
  stroke-width: 2px;
  stroke: var(--bs-primary);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.ico_btn_link:hover .btn_icon {
  -webkit-transform: translateX(3px);
          transform: translateX(3px);
}
.ico_btn_link:hover .btn_icon svg path:nth-child(1) {
  -webkit-transform: translate(3px, -3px);
          transform: translate(3px, -3px);
}
.ico_btn_link:hover .btn_icon svg path:nth-child(2) {
  -webkit-transform: translateX(3px);
          transform: translateX(3px);
}
.ico_btn_link:hover .btn_icon svg path:nth-child(3) {
  -webkit-transform: translate(3px, 3px);
          transform: translate(3px, 3px);
}

@-webkit-keyframes animationScrollDown {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-6px);
            transform: translateY(-6px);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    -webkit-transform: translateY(6px);
            transform: translateY(6px);
  }
}

@keyframes animationScrollDown {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-6px);
            transform: translateY(-6px);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    -webkit-transform: translateY(6px);
            transform: translateY(6px);
  }
}
.scroll_down {
  z-index: 1;
  width: 86px;
  height: 86px;
  font-size: 18px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.scroll_down i {
  display: block;
  margin: -5px 0;
  line-height: 1;
  color: var(--bs-light);
  -webkit-animation: animationScrollDown 2s infinite;
          animation: animationScrollDown 2s infinite;
}
.scroll_down i:nth-child(2) {
  -webkit-animation-delay: -0.2s;
          animation-delay: -0.2s;
}
.scroll_down i:nth-child(3) {
  -webkit-animation-delay: -0.4s;
          animation-delay: -0.4s;
}
.scroll_down .spin_image {
  top: 1px;
  left: 0;
  right: 0;
  z-index: -1;
  opacity: 0.1;
  position: absolute;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
  -webkit-animation: spin 10s linear infinite;
          animation: spin 10s linear infinite;
}
.scroll_down:hover .spin_image {
  opacity: 0.25;
}

/* 2.04 - Button - End
================================================== */
/* 2.03 - Ordered and Unordered list - Start
================================================== */
[class*=unordered_list] {
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
[class*=unordered_list] > li {
  float: left;
  list-style: none;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.unordered_list_block {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.unordered_list_block > li {
  width: 100%;
  display: block;
}

.contact_info_list {
  gap: 20px;
}
.contact_info_list a {
  gap: 10px;
  font-size: 20px;
  font-weight: 700;
  line-height: 24px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.contact_info_list a:hover {
  color: var(--bs-primary);
}
.contact_info_list a i {
  color: var(--bs-primary);
}

.category_list_block > li {
  border-bottom: 1px solid var(--bs-border-color);
}
.category_list_block > li > a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 12px 0;
  font-size: 16px;
  list-style: 24px;
  font-weight: 700;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.category_list_block > li > a > * {
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.category_list_block > li:hover > a > * {
  color: var(--bs-secondary);
}
.category_list_block > li:hover > a .label {
  -webkit-transform: translateX(18px);
          transform: translateX(18px);
}
.category_list_block > li:hover > a .icon {
  opacity: 1;
}
.category_list_block .icon {
  left: 0;
  top: 13px;
  opacity: 0;
  position: absolute;
}

.tags_block {
  gap: 10px;
}
.tags_block a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  padding: 6px 16px;
  border-radius: 6px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.tags_block a:hover {
  color: var(--bs-white);
  border-color: var(--bs-secondary);
  background-color: var(--bs-secondary);
}

.post_meta {
  gap: 14px 40px;
}
.post_meta > li {
  line-height: 1;
  font-size: 16px;
  font-weight: 500;
}
.post_meta a {
  gap: 8px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.post_meta a:hover {
  color: var(--bs-primary);
}
.post_meta a.post_category {
  font-weight: 600;
  color: var(--bs-primary);
  text-transform: uppercase;
}
.post_meta i {
  color: var(--bs-primary);
}

.post_meta.style_2 > li {
  position: relative;
}
.post_meta.style_2 > li:not(:last-child):after {
  top: 0;
  bottom: 0;
  width: 2px;
  content: "";
  right: -21px;
  position: absolute;
  background-color: #DADAE5;
}

/* 2.03 - Ordered and Unordered list - End
================================================== */
/* 5.01 - Home Pages - Start
================================================== */
.index_ico {
  --bs-body-font-family: "DM Sans", serif;
  --bs-heading-font-family: "Roobert PRO Bold";
  --bs-body-bg: #070710;
  --bs-body-bg-rgb: 7, 7, 16;
  --bs-body-color: #D4D5F1;
  --bs-body-color-rgb: 212, 213, 241;
  --bs-heading-color: #CCCEEF;
  --bs-light: #0F1021;
  --bs-light-rgb: 15, 16, 33;
  --bs-primary: #FF3BD4;
  --bs-primary-rgb: 255, 59, 212;
  --bs-secondary: #7130C3;
  --bs-secondary-rgb: 113, 48, 195;
  --bs-border-color: #2F336D;
  --bs-border-color-translucent: 47, 51, 109;
  --bs-dark: #070710;
  --bs-dark-rgb: 7, 7, 16;
}
.index_ico .mobile_menu_btn:not(:hover) {
  border-color: rgba(255, 255, 255, 0.3);
}
.index_ico .dropdown-menu > li.active > .dropdown-item, .index_ico .dropdown-menu > li:hover > .dropdown-item {
  background-color: #0f1021;
}
.index_ico .iframe_block {
  background-color: var(--bs-secondary);
}
.index_ico .iframe_block iframe {
  mix-blend-mode: luminosity;
}
.index_ico .progress {
  background-color: rgba(255, 255, 255, 0.1);
}
.index_ico .progress .progress-bar {
  background: -webkit-gradient(linear, left top, right top, from(var(--bs-primary)), to(var(--bs-secondary)));
  background: linear-gradient(90deg, var(--bs-primary), var(--bs-secondary));
}
.index_ico .blog_section,
.index_ico .contact_section,
.index_ico .register_section,
.index_ico [class*=_details_section] {
  --bs-border-color: #21234B;
  --bs-border-color-translucent: 33, 35, 75;
}

.index_memecoin {
  --bs-body-font-family: "Bricolage Grotesque", serif;
  --bs-heading-font-family: "Dela Gothic One", serif;
  --bs-body-bg: #040612;
  --bs-body-bg-rgb: 4, 6, 18;
  --bs-body-color: #DCDEE9;
  --bs-body-color-rgb: 220, 222, 233;
  --bs-heading-color: #FFFFFF;
  --bs-light: #C5F57D;
  --bs-light-rgb: 197, 245, 125;
  --bs-primary: #0E9462;
  --bs-primary-rgb: 14, 148, 98;
  --bs-secondary: #FFCC00;
  --bs-secondary-rgb: 255, 204, 0;
  --bs-border-color: #0E9462;
  --bs-border-color-translucent: 14, 148, 98;
  --bs-dark: var(--bs-body-bg);
  --bs-dark-rgb: var(--bs-body-bg-rgb);
}
.index_memecoin .cb-cursor:before {
  background: var(--bs-secondary);
}
.index_memecoin h1, .index_memecoin h2, .index_memecoin h3, .index_memecoin h4, .index_memecoin h5, .index_memecoin h6 {
  font-weight: 400;
}
.index_memecoin mark {
  font-weight: 400;
  color: var(--bs-light);
}
.index_memecoin .line-1,
.index_memecoin .line-2,
.index_memecoin .line-3,
.index_memecoin .line-4 {
  background: var(--bs-primary);
}
.index_memecoin .countdown_timer_block {
  gap: 15px;
}
.index_memecoin .countdown_timer_block > li {
  gap: 4px;
  width: 100px;
  height: 80px;
  background-color: #070710;
}
.index_memecoin .countdown_timer_block > li:after {
  display: none;
}
.index_memecoin .countdown_timer_block span {
  font-size: 26px;
  font-weight: 400;
}
.index_memecoin .progress {
  background-color: rgba(255, 255, 255, 0.1);
}
.index_memecoin .progress .progress-bar {
  background: -webkit-gradient(linear, left top, right top, from(var(--bs-primary)), to(var(--bs-light)));
  background: linear-gradient(90deg, var(--bs-primary), var(--bs-light));
}
.index_memecoin .progress .progress-bar:after {
  color: var(--bs-dark);
}
.index_memecoin .social_block a {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 10px;
  color: var(--bs-white);
  background-color: var(--bs-primary);
}
.index_memecoin .social_block a:hover {
  color: var(--bs-dark);
  background-color: var(--bs-secondary);
}
.index_memecoin .pagelist_block a:hover {
  color: var(--bs-light);
}

.index_pepecoin {
  --bs-body-font-family: "Nunito Sans", serif;
  --bs-heading-font-family: "Lilita One", serif;
  --bs-body-bg: #FFF7AF;
  --bs-body-bg-rgb: 255, 247, 175;
  --bs-body-color: #113B1E;
  --bs-body-color-rgb: 17, 59, 30;
  --bs-heading-color: #113B1E;
  --bs-primary: #0B902B;
  --bs-primary-rgb: 11, 144, 43;
  --bs-secondary: #FBE354;
  --bs-secondary-rgb: 251, 227, 84;
  --bs-dark: #232222;
  --bs-dark-rgb: 35, 34, 34;
}
.index_pepecoin h1, .index_pepecoin h2, .index_pepecoin h3, .index_pepecoin h4, .index_pepecoin h5, .index_pepecoin h6 {
  font-weight: 400;
}
.index_pepecoin mark {
  font-weight: 400;
  color: var(--bs-secondary);
}
.index_pepecoin .backtotop .scroll {
  color: var(--bs-white);
  border: var(--bs-dark);
  background-color: var(--bs-dark);
}
.index_pepecoin .dropdown-menu {
  -webkit-box-shadow: 0 30px 50px rgba(0, 0, 0, 0.2);
          box-shadow: 0 30px 50px rgba(0, 0, 0, 0.2);
}

/* 5.01 - Home Pages - End
================================================== */
/* 3.01 - Site Header - Start
================================================== */
.site_header {
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  position: fixed;
}
.site_header .nav_wrapper {
  padding: 30px 0;
  -webkit-transition: padding 0.3s;
  transition: padding 0.3s;
}
.site_header.sticky .nav_wrapper {
  padding: 10px 0;
  -webkit-backdrop-filter: saturate(180%) blur(20px);
          backdrop-filter: saturate(180%) blur(20px);
}
.site_header .ico_btn_outline {
  font-size: 16px;
  padding: 14px 26px;
}
.site_header .language_dropdown .language_dropdown {
  right: 0;
  left: auto;
}

.header_memecoin .main_menu_list {
  gap: 40px;
}
.header_memecoin .main_menu_list > li > .nav-link {
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0.4px;
}
.header_memecoin .main_menu_list > li > .nav-link > .nav_link_icon {
  margin: 0;
}
.header_memecoin .main_menu_list > li:hover > .nav-link,
.header_memecoin .main_menu_list > li.active > .nav-link {
  color: var(--bs-light);
}
.header_memecoin .main_menu_list > li:hover > .nav-link .nav_link_label {
  -webkit-transform: translateY(-100%);
          transform: translateY(-100%);
}
.header_memecoin .memecoin_btn {
  font-size: 18px;
  padding: 13px 30px;
}

.header_pepecoin.sticky .site_logo .site_link > img:nth-child(1), .header_pepecoin:not(.sticky) .site_logo .site_link > img:nth-child(2) {
  display: none;
}
.header_pepecoin .btns_group {
  margin-left: 70px;
}
.header_pepecoin .pepecoin_btn {
  font-size: 20px;
  padding: 17px 30px;
}
.header_pepecoin:not(.sticky) .main_menu_list > li > .nav-link {
  color: var(--bs-white);
}
.header_pepecoin:not(.sticky) .main_menu_list .nav_link_icon {
  color: var(--bs-white);
}
.header_pepecoin .main_menu_list > li > .nav-link {
  line-height: 1;
  font-size: 18px;
  font-weight: 400;
}
.header_pepecoin .main_menu_list .dropdown-item {
  font-weight: 400;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}
.header_pepecoin .nav_wrapper {
  padding: 60px 0;
}
.header_pepecoin.sticky .nav_wrapper {
  padding: 5px 0;
}

.main_menu_list {
  gap: 46px;
}
.main_menu_list > li > .nav-link {
  gap: 4px;
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  line-height: 1;
  font-size: 16px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  font-weight: 500;
  line-height: 18px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  overflow: hidden;
  color: var(--bs-heading-color);
  font-family: var(--bs-heading-font-family);
}
.main_menu_list > li > .nav-link > .nav_link_label {
  padding: 1px 0;
  position: relative;
  display: inline-block;
  -webkit-transition: -webkit-transform 0.3s;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}
.main_menu_list > li > .nav-link > .nav_link_label:before {
  left: 0;
  top: 100%;
  text-align: center;
  position: absolute;
  content: attr(data-text);
}
.main_menu_list > li > .nav-link > .nav_link_icon {
  margin: 1px 0 0;
}
.main_menu_list > li.active > .nav-link {
  color: var(--bs-primary);
}
.main_menu_list > li:hover > .nav-link {
  color: var(--bs-primary);
}
.main_menu_list > li:hover > .nav-link .nav_link_label {
  -webkit-transform: translateY(-90%);
          transform: translateY(-90%);
}
.main_menu_list > li:hover > .nav-link > .nav_link_icon {
  -webkit-transform: rotateX(-180deg);
          transform: rotateX(-180deg);
}
.main_menu_list .nav_link_icon {
  font-size: 16px;
  fill: currentColor;
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.main_menu_list .dropdown-menu {
  min-width: 220px;
}
.main_menu_list .dropdown-menu .dropdown > .dropdown-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.main_menu_list .dropdown-menu .dropdown-menu {
  top: 0;
  margin: 0;
  left: 100%;
}

@media screen and (min-width: 992px) {
  .site_header .btns_group {
    gap: 24px;
  }
  .main_menu_list .dropdown-menu {
    opacity: 0;
    display: block;
    -webkit-transition: 200ms;
    transition: 200ms;
    visibility: hidden;
    -webkit-transform-origin: top;
            transform-origin: top;
    -webkit-transform: perspective(300px) rotateX(-8deg);
            transform: perspective(300px) rotateX(-8deg);
  }
  .main_menu_list .dropdown:hover > .dropdown-menu {
    opacity: 1;
    visibility: visible;
    -webkit-transform: perspective(300px) rotateX(0deg);
            transform: perspective(300px) rotateX(0deg);
  }
  .main_menu_list > .dropdown > .dropdown-menu {
    margin-top: 36px;
  }
  .main_menu_list > .dropdown > .dropdown-menu:before {
    left: 0;
    right: 0;
    top: -20px;
    content: "";
    height: 20px;
    display: block;
    position: absolute;
  }
  .site_header.sticky .main_menu_list > .dropdown > .dropdown-menu {
    margin-top: 42px;
  }
  .site_header.sticky .main_menu_list > .dropdown > .dropdown-menu:before {
    top: -22px;
    height: 22px;
  }
}
.mobile_menu_btn {
  width: 44px;
  height: 44px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  font-size: 20px;
  border-radius: 6px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  color: var(--bs-heading-color);
  border: 1px solid var(--bs-border-color);
}
.mobile_menu_btn:hover {
  color: var(--bs-white);
  border-color: var(--bs-primary);
  background-color: var(--bs-primary);
}

@media screen and (max-width: 1199px) {
  .main_menu_list {
    gap: 40px;
  }
}
@media screen and (max-width: 1024px) {
  .main_menu_list {
    gap: 20px;
  }
  .header_memecoin .main_menu_list {
    gap: 16px;
  }
  .header_pepecoin .nav_wrapper {
    padding: 24px 0;
  }
  .header_pepecoin .pepecoin_btn {
    font-size: 18px;
  }
}
@media screen and (max-width: 991px) {
  .site_header .btns_group {
    gap: 10px;
  }
  .site_header .main_menu {
    left: 0;
    right: 0;
    top: 104px;
    z-index: 999;
    position: fixed;
    padding: 0 15px;
  }
  .site_header.sticky .main_menu {
    top: 64px;
  }
  .main_menu_list {
    gap: 1px;
    margin: auto;
    max-width: 700px;
    border-radius: 6px;
    padding: 15px 30px;
    background-color: var(--bs-white);
    -webkit-box-shadow: 0 5px 20px 0 rgba(0, 0, 0, 0.6);
            box-shadow: 0 5px 20px 0 rgba(0, 0, 0, 0.6);
  }
  .main_menu_list > li:not(:hover) > .nav-link,
  .main_menu_list > li:not(.active) > .nav-link {
    color: var(--bs-dark);
  }
  .main_menu_list > li {
    width: 100%;
    display: block;
    padding: 15px 0;
  }
  .main_menu_list > li:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }
  .main_menu_list > li > a {
    font-size: 18px;
  }
  .main_menu_list .dropdown-menu {
    position: static;
    -webkit-box-shadow: none;
            box-shadow: none;
  }
  .header_memecoin .main_menu {
    top: 110px;
  }
  .header_memecoin.sticky .main_menu {
    top: 70px;
  }
  .header_memecoin .main_menu_list > li:hover > .nav-link,
  .header_memecoin .main_menu_list > li.active > .nav-link {
    color: var(--bs-primary);
  }
  .header_memecoin .main_menu_list {
    gap: 0;
  }
  .header_pepecoin:not(.sticky) .main_menu_list > li > .nav-link,
  .header_pepecoin:not(.sticky) .main_menu_list .nav_link_icon {
    color: var(--bs-dark);
  }
  .header_pepecoin .main_menu {
    top: 100px;
  }
  .header_pepecoin.sticky .main_menu {
    top: 62px;
  }
}
@media screen and (max-width: 575px) {
  .header_memecoin .memecoin_btn {
    font-size: 14px;
    padding: 10px 24px;
  }
  .header_memecoin .btns_group > li:last-child {
    display: none;
  }
  .header_memecoin .main_menu {
    top: 104px;
  }
  .header_memecoin.sticky .main_menu {
    top: 64px;
  }
}
@media screen and (max-width: 425px) {
  .header_pepecoin .pepecoin_btn {
    font-size: 16px;
    padding: 12px 24px;
  }
  .mobile_menu_btn {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  .header_pepecoin .main_menu {
    top: 88px;
  }
  .header_pepecoin.sticky .main_menu {
    top: 50px;
  }
}
/* 3.01 - Site Header - End
================================================== */
.index_ico {
  --bs-light: #222245;
  --bs-light-rgb: 34, 34, 69;
  --bs-border-color: #2B3079;
}

@font-face {
  font-family: "Roobert PRO Bold";
  src: url("../assets/fonts/RoobertPRO-Bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Roobert PRO Medium";
  src: url("../assets/fonts/RoobertPRO-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "January Night";
  src: url("../assets/fonts/January-Night.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
.heading_block {
  margin-bottom: 63px;
}
.heading_block .heading_text {
  font-size: 52px;
  line-height: 62px;
  margin-bottom: 18px;
}
.heading_block .heading_description {
  font-size: 18px;
  line-height: 26px;
}

.site_header .nav_wrapper {
  background-color: #222245;
}
.site_header .ico_btn_outline .btn_icon {
  width: 14px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}
.site_header .ico_btn_outline .btn_icon svg {
  width: 100%;
  display: block;
  fill: var(--bs-body-color);
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.site_header .ico_btn_outline:hover .btn_icon svg {
  fill: var(--bs-white);
}

.hero_section {
  z-index: 2;
  overflow: hidden;
  position: relative;
  padding: 172px 0 106px;
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: center top;
}
.hero_section .noise_image {
  inset: 0;
  z-index: -2;
  opacity: 0.05;
  position: absolute;
}
.hero_section .color_overlay {
  --bs-secondary: #7130C3;
  inset: 0;
  z-index: -1;
  position: absolute;
  background-image: -webkit-gradient(linear, left bottom, left top, from(var(--bs-secondary)), color-stop(70%, transparent));
  background-image: linear-gradient(0deg, var(--bs-secondary) 0%, transparent 70%);
}
.hero_section h1 {
  font-size: 60px;
  font-weight: 400;
  line-height: 70px;
  margin-bottom: 8px;
  font-family: "Roobert PRO Medium";
}
.hero_section h1 strong {
  font-weight: 700;
  font-family: "Roobert PRO Bold";
}
.hero_section p {
  font-size: 20px;
  font-weight: 500;
  line-height: 32px;
  margin-bottom: 52px;
}
.hero_section .hero_image_slider {
  border-radius: 18px;
  margin-bottom: 60px;
  border: 3px solid rgba(255, 255, 255, 0.12);
  -webkit-box-shadow: 0 30px 67px 0 rgba(16, 24, 40, 0.21);
          box-shadow: 0 30px 67px 0 rgba(16, 24, 40, 0.21);
}
.hero_section:has(.home_ico.swiper-slide-active) .color_overlay {
  --bs-secondary: #7130C3;
}
.hero_section:has(.home_meme.swiper-slide-active) .color_overlay {
  --bs-secondary: #0D9565;
}
.hero_section:has(.home_pepe.swiper-slide-active) .color_overlay {
  --bs-secondary: #079529;
}
.hero_section .scroll_down i {
  color: var(--bs-white);
}
.hero_section .shape_piggy_bank {
  right: 44px;
  bottom: 76px;
  max-width: 106px;
}
.hero_section .shape_piggy_bank img {
  display: block;
  -webkit-animation: upDownMover 3.5s ease-in-out infinite;
          animation: upDownMover 3.5s ease-in-out infinite;
}
.hero_section .shape_coin {
  left: 12px;
  bottom: 58px;
  max-width: 146px;
}
.hero_section .shape_coin img {
  display: block;
  -webkit-animation: upDownMover 3.5s ease-in-out infinite;
          animation: upDownMover 3.5s ease-in-out infinite;
}
.hero_section .badge_gurantee {
  z-index: 2;
  text-align: right;
  position: relative;
  margin: 30px -40px -70px 0;
}
.hero_section .badge_gurantee img {
  max-width: 134px;
  display: inline-block;
}

.home_demo_section {
  margin-top: -36px;
}
.home_demo_section .container {
  z-index: 2;
  position: relative;
}
.home_demo_section [class*=shape_circle_] {
  z-index: 1;
  opacity: 0.3;
  max-width: 600px;
  -webkit-filter: blur(200px);
          filter: blur(200px);
}
.home_demo_section .shape_circle_1 {
  top: 200px;
  left: -300px;
}
.home_demo_section .shape_circle_2 {
  top: 150px;
  right: -300px;
}
.home_demo_section .shape_circle_3 {
  top: 65%;
  left: 50%;
  -webkit-transform: translate(-50%, -65%);
          transform: translate(-50%, -65%);
}

.container:has(.template_card_wrapper) {
  max-width: 1750px;
}
.container:has(.template_card_wrapper) > .row {
  margin: -75px;
}
.container:has(.template_card_wrapper) > .row > * {
  padding: 75px;
}

.template_card_wrapper {
  position: relative;
  padding-right: 90px;
}
.template_card_wrapper .mobile_mockup {
  top: 70px;
  z-index: 2;
  right: -15px;
  max-width: 180px;
  position: absolute;
}
.template_card_wrapper:has(.upcoming_item) .mobile_mockup {
  -webkit-filter: blur(10px);
          filter: blur(10px);
}

.template_card {
  padding: 30px;
  border-radius: 15px;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.template_card .template_card_image {
  display: block;
  overflow: hidden;
  position: relative;
  border-radius: 10px;
  margin-bottom: 28px;
}
.template_card .template_card_image img {
  -webkit-transition: var(--bs-transition);
  transition: var(--bs-transition);
}
.template_card .template_card_image:hover img {
  -webkit-transform: scale(1.08);
          transform: scale(1.08);
}
.template_card .template_card_info {
  position: relative;
}
.template_card .template_card_title {
  font-size: 30px;
  line-height: 44px;
  margin-bottom: 3px;
}
.template_card .btn {
  top: 0;
  right: 0;
  z-index: 1;
  border: none;
  font-size: 20px;
  overflow: hidden;
  font-weight: 700;
  position: relative;
  padding: 19px 30px;
  position: absolute;
  -webkit-transition: 0.4s ease-in-out;
  transition: 0.4s ease-in-out;
  background-color: var(--bs-dark);
}
.template_card .btn:before {
  top: 50%;
  left: 50%;
  opacity: 0;
  content: "";
  z-index: -1;
  width: 80px;
  height: 80px;
  -webkit-filter: blur(20px);
          filter: blur(20px);
  position: absolute;
  border-radius: 100%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  -webkit-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out;
  background-color: var(--bs-primary);
}
.template_card .btn:hover {
  background-color: var(--bs-secondary);
}
.template_card .btn:hover:before {
  opacity: 1;
}
.template_card.upcoming_item .template_card_image {
  border: 1px solid rgba(255, 255, 255, 0.13);
}
.template_card.upcoming_item .template_card_image img {
  -webkit-transform: inherit;
          transform: inherit;
}
.template_card.upcoming_item .template_card_image:before {
  inset: 0;
  z-index: 1;
  content: "";
  position: absolute;
  background-size: 90%;
  border-radius: inherit;
  -webkit-backdrop-filter: blur(30px);
          backdrop-filter: blur(30px);
  background-repeat: no-repeat;
  background-position: center bottom 28px;
  background-image: url(images/home_pages/coming_soon.webp);
}

.feature_section .container {
  z-index: 2;
  position: relative;
}
.feature_section [class*=shape_circle_] {
  z-index: 1;
  opacity: 0.3;
  max-width: 400px;
  -webkit-filter: blur(200px);
          filter: blur(200px);
}
.feature_section .shape_circle_1 {
  top: 10px;
  left: -200px;
}
.feature_section .shape_circle_2 {
  top: 0;
  right: -200px;
}
.feature_section .shape_circle_3 {
  left: 50%;
  bottom: 0;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.feature_section .shape_locker {
  right: 44px;
  bottom: 20px;
  max-width: 110px;
}
.feature_section .shape_locker img {
  display: block;
  -webkit-animation: upDownMover 3.5s ease-in-out infinite;
          animation: upDownMover 3.5s ease-in-out infinite;
}
.feature_section .shape_wallet {
  bottom: 0;
  left: 12px;
  max-width: 110px;
}
.feature_section .shape_wallet img {
  display: block;
  -webkit-animation: upDownMover 3.5s ease-in-out infinite;
          animation: upDownMover 3.5s ease-in-out infinite;
}

.row:has(.feature_iconbox_block) {
  margin: -2px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.row:has(.feature_iconbox_block) > * {
  width: 20%;
  padding: 2px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}

.feature_iconbox_block {
  min-height: 234px;
  padding: 40px 24px;
  text-align: center;
  border-radius: 30px;
  background-color: var(--bs-light);
  border: 1px solid var(--bs-border-color);
}
.feature_iconbox_block .icon {
  height: 60px;
  margin-bottom: 28px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.feature_iconbox_block .icon img {
  width: auto;
  height: auto;
}
.feature_iconbox_block .title {
  font-size: 24px;
  line-height: 32px;
}

.color_section [class*=shape_circle_] {
  opacity: 0.3;
  max-width: 700px;
  -webkit-filter: blur(200px);
          filter: blur(200px);
}
.color_section .shape_circle_1 {
  top: 162px;
  left: -400px;
}
.color_section .shape_circle_2 {
  bottom: 12px;
  right: -400px;
}

.color_iconbox_group {
  margin: -25px;
}
.color_iconbox_group > li {
  padding: 25px;
}
.color_iconbox_group .color_iconbox_block {
  max-width: 290px;
}

.color_iconbox_block .icon {
  max-width: 44px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.color_iconbox_block .title {
  font-size: 24px;
  line-height: 34px;
  margin: 15px 0 12px;
}
.color_iconbox_block p {
  font-size: 16px;
  line-height: 26px;
}

.responsive_section .row {
  margin: -10px;
}
.responsive_section .row > * {
  padding: 10px;
}
.responsive_section .bg-light {
  overflow: hidden;
  border-radius: 20px;
  padding: 70px 70px 62px;
  border: 1px solid var(--bs-border-color);
}
.responsive_section .bg-light:not(:last-child) {
  margin-bottom: 20px;
}
.responsive_section .bg-light .heading_block .heading_text {
  font-size: 40px;
  line-height: 50px;
}
.responsive_section .bg-light .heading_block .heading_description {
  font-size: 18px;
  max-width: 325px;
  line-height: 26px;
}
.responsive_section .color_iconbox_block {
  max-width: 270px;
}
.responsive_section .layout_laptop_image {
  text-align: right;
}
.responsive_section .layout_laptop_image img {
  margin-right: -76px;
}
.responsive_section .layout_tablet_image {
  text-align: right;
}
.responsive_section .layout_tablet_image img {
  margin-right: -70px;
}
.responsive_section .layout_mobile_image {
  max-width: 265px;
}
.responsive_section [class*=shape_circle_] {
  opacity: 0.3;
  max-width: 700px;
  -webkit-filter: blur(200px);
          filter: blur(200px);
}
.responsive_section .shape_circle_1 {
  top: 30%;
  left: -350px;
}
.responsive_section .shape_circle_2 {
  top: 10%;
  right: -350px;
}
.responsive_section .shape_circle_3 {
  top: 98%;
  left: -350px;
}
.responsive_section .shape_circle_4 {
  top: 86%;
  right: -350px;
}

.device_list {
  margin-top: 20px;
}
.device_list li {
  width: 25%;
  text-align: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.device_list li:not(:last-child) {
  border-style: solid;
  border-width: 0 1px 0 0;
  border-color: var(--bs-border-color);
}

.device_block {
  text-align: center;
}
.device_block .icon {
  margin-bottom: 30px;
}
.device_block .title {
  font-size: 16px;
  font-weight: 500;
  line-height: 26px;
  font-family: "Roobert PRO Medium";
}
.device_block .title strong {
  display: block;
  font-size: 18px;
  font-weight: 700;
  font-family: "Roobert PRO Bold";
}

.review_section .row {
  margin: -5px;
}
.review_section .row > * {
  padding: 5px;
}
.review_section .shape_piggy_bank {
  right: 44px;
  bottom: 20px;
  max-width: 106px;
}
.review_section .shape_piggy_bank img {
  display: block;
  -webkit-animation: upDownMover 3.5s ease-in-out infinite;
          animation: upDownMover 3.5s ease-in-out infinite;
}
.review_section .shape_coin {
  bottom: 0;
  left: 12px;
  max-width: 146px;
}
.review_section .shape_coin img {
  display: block;
  -webkit-animation: upDownMover 3.5s ease-in-out infinite;
          animation: upDownMover 3.5s ease-in-out infinite;
}

.review_block {
  text-align: center;
  border-radius: 15px;
  background-size: cover;
  padding: 40px 30px 40px;
  background-repeat: no-repeat;
  background-position: center center;
  border: 1px solid var(--bs-border-color);
}
.review_block:not(:last-child) {
  margin-bottom: 10px;
}
.review_block .quote_icon {
  max-width: 34px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.review_block .quote_icon img {
  width: 100%;
  display: block;
}
.review_block .review_comment {
  font-size: 22px;
  max-width: 405px;
  font-weight: 500;
  line-height: 32px;
  margin: 26px auto 22px;
  font-family: "Roobert PRO Medium";
}
.review_block .review_admin {
  gap: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.review_block .review_admin .avater {
  width: 40px;
  height: 40px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  overflow: hidden;
  border-radius: 100%;
}
.review_block .review_admin .name {
  line-height: 1;
  font-size: 18px;
  font-weight: 500;
  font-family: "Roobert PRO Medium";
}

.review_carousel {
  position: relative;
}
.review_carousel [class*=rc-button-] {
  top: 50%;
  z-index: 1;
  width: 36px;
  height: 44px;
  border-radius: 5px;
  position: absolute;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  background-color: var(--bs-dark);
  border: 1px solid var(--bs-border-color);
}
.review_carousel [class*=rc-button-]:hover {
  color: var(--bs-white);
  border-color: var(--bs-secondary);
  background-color: var(--bs-secondary);
}
.review_carousel .rc-button-prev {
  left: 30px;
}
.review_carousel .rc-button-next {
  right: 30px;
}
.review_carousel .review_block {
  padding: 120px 30px 118px;
}
.review_carousel .review_block .quote_icon {
  max-width: 56px;
}
.review_carousel .review_block .review_comment {
  font-size: 24px;
  max-width: 430px;
  line-height: 36px;
  margin: 26px auto 36px;
}
.review_carousel .review_block .review_admin .avater {
  width: 50px;
  height: 50px;
}
.review_carousel .review_block .review_admin .name {
  font-size: 22px;
}

.support_section .shape_circle_1 {
  top: 46%;
  left: 44%;
  opacity: 0.3;
  max-width: 500px;
  -webkit-filter: blur(200px);
          filter: blur(200px);
}

.iconlist_block.unordered_list_block {
  gap: 20px;
}
.iconlist_block > li {
  gap: 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 16px;
  font-weight: 500;
  line-height: 26px;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  font-family: "Roobert PRO Medium";
}
.iconlist_block .iconlist_icon {
  width: 18px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}

.support_image {
  margin: 0 -20px 0 0;
}

.site_footer {
  padding: 300px 0 120px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center top;
}
.site_footer h2 {
  font-size: 70px;
  line-height: 80px;
  margin-bottom: 40px;
}
.site_footer .iconlist_block.unordered_list {
  gap: 20px 100px;
}
.site_footer .iconlist_block > li {
  font-size: 18px;
  position: relative;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.site_footer .iconlist_block > li:not(:last-child):after {
  top: 3px;
  width: 1px;
  content: "";
  right: -51px;
  height: 21px;
  position: absolute;
  background-color: var(--bs-body-color);
}
.site_footer .iconlist_block .iconlist_icon {
  width: 20px;
}

@media (max-width: 1440px) {
  .container:has(.template_card_wrapper) {
    max-width: 1320px;
  }
  .template_card {
    padding: 20px;
  }
  .container:has(.template_card_wrapper) > .row {
    margin: -30px;
  }
  .container:has(.template_card_wrapper) > .row > * {
    padding: 30px;
  }
  .template_card_wrapper .mobile_mockup {
    right: 0;
    top: 50px;
    max-width: 140px;
  }
}
@media (max-width: 1199px) {
  .feature_iconbox_block .title {
    font-size: 20px;
    line-height: 26px;
  }
  .responsive_section .bg-light {
    padding: 50px;
  }
  .responsive_section .layout_laptop_image img {
    margin-right: -55px;
  }
  .responsive_section .layout_tablet_image img {
    margin-right: -52px;
  }
  .container:has(.template_card_wrapper) > .row {
    margin: -30px -15px;
  }
  .container:has(.template_card_wrapper) > .row > * {
    padding: 30px 15px;
  }
  .template_card_wrapper .mobile_mockup {
    top: 44px;
    right: 30px;
    max-width: 130px;
  }
  .template_card .btn {
    font-size: 16px;
    padding: 15px 30px;
  }
  .row:has(.feature_iconbox_block) > * {
    width: 25%;
  }
  .feature_iconbox_block .title {
    font-size: 24px;
    line-height: 28px;
  }
  .feature_iconbox_block {
    padding: 40px 40px;
  }
}
@media (max-width: 1024px) {
  .template_card_wrapper {
    padding-right: 0;
  }
  .template_card_wrapper .mobile_mockup {
    display: none;
  }
  .container:has(.template_card_wrapper) > .row {
    margin: -15px;
  }
  .container:has(.template_card_wrapper) > .row > * {
    padding: 15px;
  }
  .feature_iconbox_block .title {
    font-size: 20px;
    line-height: 26px;
  }
  .feature_iconbox_block {
    padding: 40px 35px;
  }
  .hero_section .shape_piggy_bank,
  .hero_section .shape_coin,
  .feature_section .shape_wallet,
  .feature_section .shape_locker,
  .review_section .shape_piggy_bank,
  .review_section .shape_coin {
    display: none;
  }
  .heading_block .heading_text {
    font-size: 48px;
    line-height: 54px;
    margin-bottom: 16px;
  }
  .responsive_section .bg-light .heading_block .heading_text {
    font-size: 36px;
    line-height: 42px;
  }
  .review_carousel .rc-button-prev {
    left: 0;
  }
  .review_carousel .rc-button-next {
    right: 0;
  }
  .site_footer h2 {
    font-size: 60px;
    line-height: 70px;
    margin-bottom: 40px;
  }
  .site_footer .iconlist_block.unordered_list {
    gap: 20px 80px;
  }
  .site_footer .iconlist_block > li:not(:last-child):after {
    right: -41px;
  }
  .site_footer {
    padding: 260px 0 120px;
  }
  .hero_section h1 {
    font-size: 54px;
    line-height: 64px;
  }
  .hero_section .badge_gurantee img {
    max-width: 110px;
    display: inline-block;
  }
  .hero_section {
    padding: 200px 0 120px;
  }
}
@media (max-width: 991px) {
  .container {
    max-width: 730px;
  }
  .hero_section h1 {
    font-size: 48px;
    line-height: 58px;
  }
  .hero_section p {
    font-size: 18px;
    line-height: 28px;
    margin-bottom: 40px;
  }
  .hero_section .hero_image_slider {
    margin-bottom: 60px;
  }
  .row:has(.feature_iconbox_block) > * {
    width: 33.333%;
  }
  .feature_iconbox_block .title {
    font-size: 18px;
  }
  .responsive_section .row {
    margin: -30px -15px;
  }
  .responsive_section .row > * {
    padding: 30px 15px;
  }
  .device_list ul {
    margin: -30px;
  }
  .device_list li {
    width: 50%;
    padding: 30px;
  }
  .site_footer h2 {
    font-size: 48px;
    line-height: 58px;
  }
  [class*=shape_circle_],
  .section_shadow_top:before {
    display: none;
  }
}
@media (max-width: 767px) {
  .template_card .btn {
    position: sticky;
    margin-top: 20px;
  }
  .site_header .btns_group > li:last-child {
    display: none;
  }
}
@media (max-width: 575px) {
  .hero_section h1 {
    font-size: 42px;
    line-height: 52px;
  }
  .hero_section .badge_gurantee img {
    max-width: 90px;
    display: inline-block;
  }
  .hero_section .badge_gurantee {
    margin: 30px 15px -50px 0;
  }
  .hero_section {
    padding: 170px 0 90px;
  }
  .section_space {
    padding-top: 120px;
    padding-bottom: 120px;
  }
  .heading_block .heading_text {
    font-size: 36px;
    line-height: 46px;
    margin-bottom: 10px;
  }
  .feature_iconbox_block .icon img {
    max-height: 100%;
  }
  .feature_iconbox_block .icon {
    height: 46px;
    margin-bottom: 20px;
  }
  .feature_iconbox_block {
    padding: 30px;
    min-height: auto;
    border-radius: 16px;
  }
  .feature_iconbox_block .title {
    font-size: 16px;
    line-height: 20px;
  }
  .responsive_section .bg-light .heading_block .heading_text {
    font-size: 30px;
    line-height: 36px;
  }
  .site_footer h2 {
    font-size: 36px;
    line-height: 46px;
  }
  .heading_block {
    margin-bottom: 50px;
  }
}
@media (max-width: 425px) {
  .site_header .nav_wrapper {
    padding: 20px 0;
  }
  .site_header .main_menu {
    top: 80px;
  }
  .site_header.sticky .main_menu {
    top: 60px;
  }
  .hero_section {
    padding: 140px 0 90px;
  }
  .hero_section h1 {
    font-size: 36px;
    line-height: 42px;
  }
  .hero_section .badge_gurantee img {
    max-width: 70px;
    display: inline-block;
  }
  .hero_section .badge_gurantee {
    margin: 50px 15px -35px 0;
  }
  .scroll_down {
    width: 70px;
    height: 70px;
    font-size: 14px;
  }
  .heading_block .heading_text {
    font-size: 30px;
    line-height: 36px;
    margin-bottom: 8px;
  }
  .heading_block .heading_description {
    font-size: 16px;
    line-height: 24px;
  }
  .row:has(.feature_iconbox_block) > * {
    width: 50%;
  }
  .feature_iconbox_block .icon {
    height: 36px;
  }
  .feature_iconbox_block .title {
    font-size: 14px;
    line-height: 18px;
  }
  .responsive_section .bg-light {
    padding: 40px;
  }
  .responsive_section .layout_laptop_image img,
  .responsive_section .layout_tablet_image img {
    margin-right: -42px;
  }
  .device_list li {
    width: 100%;
  }
  .device_list li:not(:last-child) {
    border-width: 0 0 1px 0;
  }
  .review_carousel .review_block .review_comment {
    font-size: 20px;
    line-height: 30px;
  }
  .site_footer h2 {
    font-size: 30px;
    line-height: 36px;
  }
}