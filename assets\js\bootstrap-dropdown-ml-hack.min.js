!function(o){const e="has-child-dropdown-show";var t;o.Dropdown.prototype.toggle=(t=o.Dropdown.prototype.toggle,function(){document.querySelectorAll("."+e).forEach((function(o){o.classList.remove(e)}));let o=this._element.closest(".dropdown").parentNode.closest(".dropdown");for(;o&&o!==document;o=o.parentNode.closest(".dropdown"))o.classList.add(e);return t.call(this)}),document.querySelectorAll(".dropdown").forEach((function(o){o.addEventListener("hide.bs.dropdown",(function(o){this.classList.contains(e)&&(this.classList.remove(e),o.preventDefault()),o.stopPropagation()}))})),document.querySelectorAll(".dropdown-hover, .dropdown-hover-all .dropdown").forEach((function(t){t.addEventListener("mouseenter",(function(n){let r=n.target.querySelector(':scope>[data-bs-toggle="dropdown"]');r.classList.contains("show")||(o.Dropdown.getOrCreateInstance(r).toggle(),t.classList.add(e),o.Dropdown.clearMenus())})),t.addEventListener("mouseleave",(function(e){let t=e.target.querySelector(':scope>[data-bs-toggle="dropdown"]');t.classList.contains("show")&&o.Dropdown.getOrCreateInstance(t).toggle()}))}))}(bootstrap);